import { 
  StreamRequest, 
  StreamConfig, 
  DEFAULT_STREAM_CONFIG, 
  StreamingState,
  validateStreamRequest,
  createTimeoutPromise
} from '../utils/streaming-utils';
import { parse } from 'best-effort-json-parser';

/**
 * Service for handling AI streaming operations
 * Replicates EXACT functionality from the working use-ai-streaming hook
 * Maintains 100% compatibility - no improvements, just DRY extraction
 */
export class StreamingService {
  private abortController: AbortController | null = null;
  private reader: ReadableStreamDefaultReader<Uint8Array> | null = null;
  private retryCount: number = 0;
  private timeoutHandle: NodeJS.Timeout | null = null;
  private config: Required<StreamConfig>;

  constructor(config: StreamConfig = {}) {
    this.config = { ...DEFAULT_STREAM_CONFIG, ...config };
  }

  /**
   * Execute a streaming request - EXACT replica of current hook logic
   * @param url API endpoint URL
   * @param requestData Request payload
   * @param onUpdate Callback for state updates
   */
  async stream<T = any>(
    url: string,
    requestData: StreamRequest,
    onUpdate: (updates: Partial<StreamingState<T>>) => void
  ): Promise<void> {
    // Validate request
    if (!validateStreamRequest(requestData)) {
      throw new Error('Invalid stream request structure');
    }

    // Reset retry count for new stream
    this.retryCount = 0;
    
    // Start streaming connection
    await this.createStreamingConnection(url, requestData, onUpdate);
  }

  /**
   * Abort the current streaming request - EXACT replica
   */
  abort(): void {
    try {
      // Abort any ongoing request first
      if (this.abortController) {
        console.log('[StreamingService] Aborting current request');
        this.abortController.abort();
        this.abortController = null;
      }

      // Clear reader reference without calling releaseLock
      // The abort signal will handle the cleanup
      if (this.reader) {
        this.reader = null;
      }

      if (this.timeoutHandle) {
        clearTimeout(this.timeoutHandle);
        this.timeoutHandle = null;
      }
    } catch (cleanupError) {
      // Ignore cleanup errors - they're usually harmless (EXACT replica)
      console.debug('Cleanup error (harmless):', cleanupError);
    }
  }

  /**
   * Check if a request is currently active
   */
  isActive(): boolean {
    return this.abortController !== null;
  }

  /**
   * Create fetch-based streaming connection - EXACT replica of current hook
   */
  private async createStreamingConnection<T>(
    url: string, 
    requestData: StreamRequest,
    onUpdate: (updates: Partial<StreamingState<T>>) => void
  ): Promise<void> {
    try {
      // Create abort controller for this request
      this.abortController = new AbortController();

      // Set up timeout - EXACT replica
      this.timeoutHandle = setTimeout(() => {
        onUpdate({ 
          error: 'Streaming connection timeout',
          isStreaming: false,
          isComplete: true
        });
        this.abort();
      }, this.config.timeout);

      // Make POST request with streaming - EXACT replica
      const response = await fetch(url, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'text/event-stream',
          'Cache-Control': 'no-cache'
        },
        body: JSON.stringify(requestData),
        signal: this.abortController.signal
      });

      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      if (!response.body) {
        throw new Error('No response body available for streaming');
      }

      // Reset retry count on successful connection
      this.retryCount = 0;

      // Clear timeout since connection is established
      if (this.timeoutHandle) {
        clearTimeout(this.timeoutHandle);
        this.timeoutHandle = null;
      }

      // Process streaming response - EXACT replica
      const reader = response.body.getReader();
      this.reader = reader;
      const decoder = new TextDecoder();

      try {
        while (true) {
          // Check if we should abort before reading
          if (this.abortController?.signal.aborted) {
            break;
          }

          const { done, value } = await reader.read();

          if (done) {
            // Stream completed successfully
            console.log('Stream completed naturally');
            onUpdate({ 
              isComplete: true,
              isStreaming: false 
            });
            break;
          }

          const chunk = decoder.decode(value, { stream: true });

          // Parse SSE data - EXACT replica
          const lines = chunk.split('\n');
          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.substring(6));

                // Process the event using EXACT replica logic
                this.handleStreamEvent(data, onUpdate);
              } catch (parseError) {
                console.warn('Failed to parse SSE data:', line);
              }
            }
          }
        }
      } catch (readError) {
        // Don't log errors if the request was aborted
        if (!this.abortController?.signal.aborted) {
          console.error('Error reading stream:', readError);
          throw readError;
        }
      } finally {
        // Clean up reader reference and set streaming to false
        this.reader = null;
        onUpdate({ isStreaming: false });
      }

    } catch (error) {
      // Don't retry if the request was aborted
      if (error instanceof Error && (error.name === 'AbortError' || error.message.includes('aborted'))) {
        console.log('Streaming connection aborted - this is normal during cleanup');
        onUpdate({ isStreaming: false });
        return;
      }

      console.error('Streaming connection error:', error);
      onUpdate({ isStreaming: false });
      this.handleConnectionError(url, requestData, onUpdate);
    }
  }

  /**
   * Handle stream events - EXACT replica of current hook logic
   */
  private handleStreamEvent<T>(
    streamEvent: any,
    onUpdate: (updates: Partial<StreamingState<T>>) => void
  ): void {
    try {
      // Stream event received: ${streamEvent.type}

      switch (streamEvent.type) {
                  case 'text_chunk':
            if (streamEvent.content) {
              // EXACT replica: Handle jsonArrayPath with best-effort parsing
              onUpdate({
                streamingText: streamEvent.content,
                // Note: We'll handle the accumulation and parsing in the hook
                // to maintain exact compatibility with current behavior
              });
            }
            break;

          case 'structured_data':
            if (streamEvent.data) {
              // EXACT replica: Pass the structured data for processing in hook
              onUpdate({
                partialData: streamEvent.data,
                // Additional metadata for compatibility
                _structuredDataMeta: {
                  index: (streamEvent as any).index,
                  dataType: (streamEvent as any).field,
                  itemKeys: Object.keys(streamEvent.data)
                }
              } as any);
            }
            break;

          case 'structured_complete':
            if (streamEvent.data) {
              onUpdate({ finalData: streamEvent.data as T });
            }
            onUpdate({ 
              isComplete: true,
              isStreaming: false 
            });
            break;

          case 'completion':
            onUpdate({ 
              finalData: streamEvent.data,
              isComplete: true,
              isStreaming: false 
            });
            break;

          case 'error':
            console.error(`[StreamingService] Error event:`, streamEvent.message);
            onUpdate({ 
              error: streamEvent.message || 'Unknown streaming error',
              isStreaming: false 
            });
            break;

          default:
            console.warn('[StreamingService] Unknown stream event type:', streamEvent.type);
      }
    } catch (parseError) {
      console.error('[StreamingService] Failed to parse stream event:', parseError);
      onUpdate({ 
        error: 'Failed to parse streaming response',
        isStreaming: false 
      });
      this.abort();
    }
  }

  /**
   * Handle connection errors with retry logic - EXACT replica with exponential backoff
   */
  private handleConnectionError<T>(
    url: string, 
    requestData: StreamRequest,
    onUpdate: (updates: Partial<StreamingState<T>>) => void
  ): void {
    if (this.retryCount < this.config.maxRetries) {
      this.retryCount += 1;
      // EXACT replica: Exponential backoff
      const delay = this.config.retryDelay * Math.pow(2, this.retryCount - 1);

      console.log(`Retrying connection (attempt ${this.retryCount}/${this.config.maxRetries}) in ${delay}ms`);

      this.timeoutHandle = setTimeout(() => {
        // Retry by creating a new streaming connection
        this.createStreamingConnection(url, requestData, onUpdate);
      }, delay);
    } else {
      onUpdate({ 
        error: 'Failed to establish streaming connection after maximum retries',
        isStreaming: false 
      });
      this.abort();
    }
  }
}

// Export a singleton instance for convenience
export const streamingService = new StreamingService(); 