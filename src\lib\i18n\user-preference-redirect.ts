/**
 * User preference-based redirect utilities for server-only i18n
 * Handles redirecting users to their preferred locale when visiting root paths
 */

import { redirect } from 'next/navigation';
import { headers } from 'next/headers';
import { getServerAuthState } from '@/features/auth/services/auth-state.service';
import { getCurrentUserProfile } from '@/features/user-auth-data/services/profile.service';
import type { SupportedLocale } from '@/lib/i18n/types/i18n';

/**
 * Parse Accept-Language header to get preferred locale
 * This duplicates the middleware logic to ensure consistency
 */
function parseAcceptLanguage(acceptLanguage: string): SupportedLocale {
  const supportedLocales: SupportedLocale[] = ['en', 'pt', 'es'];

  try {
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const [langCode, weight] = lang.trim().split(';');
        const code = langCode.substring(0, 2).toLowerCase();
        const priority = weight ? parseFloat(weight.split('=')[1]) : 1.0;
        return { code, priority };
      })
      .sort((a, b) => b.priority - a.priority);

    // Find the first supported language
    for (const lang of languages) {
      if (supportedLocales.includes(lang.code as SupportedLocale)) {
        return lang.code as SupportedLocale;
      }
    }
  } catch (error) {
    // If parsing fails, fall back to default
  }

  return 'en';
}

/**
 * Get user's preferred locale with proper priority system
 * Priority: 1. User DB preference (authenticated), 2. Middleware-detected locale, 3. Browser Accept-Language, 4. Default fallback
 */
async function getUserPreferredLocaleForRedirect(): Promise<SupportedLocale> {
  // 1. For authenticated users: Check user's saved language preference from database
  try {
    const { user } = await getServerAuthState();
    if (user?.id) {
      const profile = await getCurrentUserProfile(user.id);
      if (profile?.language && ['en', 'pt', 'es'].includes(profile.language)) {
        if (process.env.NODE_ENV === 'development') {
          console.log('[i18n] Redirect using authenticated user preference:', profile.language);
        }
        return profile.language as SupportedLocale;
      }
    }
  } catch (error) {
    // Auth session missing or other auth errors - user is not authenticated
    if (process.env.NODE_ENV === 'development') {
      console.log('[i18n] User not authenticated, using browser detection for redirect');
    }
  }

  // 2. For non-authenticated users: Use middleware-detected locale first
  const headersList = await headers();
  const middlewareDetectedLocale = headersList.get('x-detected-locale');

  if (middlewareDetectedLocale && ['en', 'pt', 'es'].includes(middlewareDetectedLocale)) {
    if (process.env.NODE_ENV === 'development') {
      console.log('[i18n] Redirect using middleware-detected locale:', middlewareDetectedLocale);
    }
    return middlewareDetectedLocale as SupportedLocale;
  }

  // 3. Fallback to direct Accept-Language parsing if middleware didn't set header
  const acceptLanguage = headersList.get('accept-language');

  if (acceptLanguage) {
    const browserLocale = parseAcceptLanguage(acceptLanguage);

    if (process.env.NODE_ENV === 'development') {
      console.log('[i18n] Redirect using direct browser locale detection:', {
        'accept-language': acceptLanguage,
        'detected-locale': browserLocale
      });
    }

    return browserLocale;
  }

  // 4. Default fallback
  if (process.env.NODE_ENV === 'development') {
    console.log('[i18n] Redirect using default fallback: en');
  }
  return 'en';
}

/**
 * Redirect user to their preferred locale
 * Use this in pages that don't have a locale in the URL
 *
 * @param requestedPath - The path the user is trying to access (without locale)
 * @param fallbackLocale - Fallback locale if user preference detection fails
 */
export async function redirectToUserPreferredLocale(
  requestedPath: string = '/',
  fallbackLocale: SupportedLocale = 'en'
): Promise<never> {
  try {
    // Get the user's preferred locale with proper priority system
    const preferredLocale = await getUserPreferredLocaleForRedirect();

    // Construct the localized URL
    const localizedPath = `/${preferredLocale}${requestedPath === '/' ? '' : requestedPath}`;

    if (process.env.NODE_ENV === 'development') {
      console.log('[i18n] Redirecting to:', localizedPath);
    }

    // Redirect to the localized version
    redirect(localizedPath);
  } catch (error) {
    if (process.env.NODE_ENV === 'development') {
      console.error('[i18n] Error in redirectToUserPreferredLocale:', error);
    }

    // If anything fails, redirect to fallback locale
    const fallbackPath = `/${fallbackLocale}${requestedPath === '/' ? '' : requestedPath}`;
    redirect(fallbackPath);
  }
}

/**
 * Get the preferred locale for a user without redirecting
 * Useful for conditional logic or metadata generation
 */
export async function getUserPreferredLocale(): Promise<SupportedLocale> {
  try {
    return await getUserPreferredLocaleForRedirect();
  } catch (error) {
    return 'en'; // Safe fallback
  }
}

/**
 * Check if a user should be redirected based on their preferences
 * Returns the redirect URL if needed, null if no redirect required
 */
export async function getRedirectUrlForUserPreference(
  currentPath: string,
  currentLocale?: string
): Promise<string | null> {
  try {
    const preferredLocale = await getUserPreferredLocaleForRedirect();

    // If current locale matches preferred locale, no redirect needed
    if (currentLocale === preferredLocale) {
      return null;
    }

    // If no current locale in URL, redirect to preferred locale
    if (!currentLocale) {
      return `/${preferredLocale}${currentPath === '/' ? '' : currentPath}`;
    }

    // For now, we respect URL locale over user preference for SEO
    // But this could be customized based on business requirements
    return null;
  } catch (error) {
    return null; // No redirect on error
  }
}
