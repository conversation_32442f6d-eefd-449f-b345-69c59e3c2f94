/**
 * Next.js Middleware for i18n routing and SEO optimization
 * Handles locale detection, redirects, and SEO headers
 */

import { NextRequest, NextResponse } from 'next/server';
import type { SupportedLocale } from '@/lib/i18n/types/i18n';

const locales = ['en', 'pt', 'es'] as const;

// Paths that should not be internationalized
const excludedPaths = [
  '/api',
  '/images',
  '/icons',
  '/favicon.ico',
  '/robots.txt',
  '/sitemap.xml',
  '/_next',
  '/monitoring', // Sentry tunnel
];

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Debug logging for all requests
  if (process.env.NODE_ENV === 'development') {
    console.log('[middleware] Processing request:', { pathname });
  }

  // Skip excluded paths
  if (excludedPaths.some(path => pathname.startsWith(path))) {
    if (process.env.NODE_ENV === 'development') {
      console.log('[middleware] Skipping excluded path:', pathname);
    }
    return NextResponse.next();
  }

  // Check if URL already has a locale
  const pathnameHasLocale = locales.some(
    locale => pathname.startsWith(`/${locale}/`) || pathname === `/${locale}`
  );

  if (process.env.NODE_ENV === 'development') {
    console.log('[middleware] Locale check:', { 
      pathname, 
      pathnameHasLocale,
      localeChecks: locales.map(locale => ({
        locale,
        startsWithSlash: pathname.startsWith(`/${locale}/`),
        equalsSlash: pathname === `/${locale}`
      }))
    });
  }

  if (pathnameHasLocale) {
    // Extract locale from URL
    const locale = pathname.split('/')[1] as typeof locales[number];

    // Debug logging
    if (process.env.NODE_ENV === 'development') {
      console.log('[middleware] URL has locale - setting headers:', { 
        pathname, 
        locale,
        pathnameHasLocale: true,
        localeCheck: locales.some(l => pathname.startsWith(`/${l}/`) || pathname === `/${l}`)
      });
    }

    // Create a response that passes through the request
    const response = NextResponse.next();

    // Set x-locale header for server components
    response.headers.set('x-locale', locale);
    response.headers.set('x-original-path', pathname.substring(locale.length + 1) || '/');

    return response;
  }

  // For root URLs without locale, handle redirect with authentication
  return await handleRootUrlRedirect(request, pathname);
}

/**
 * Parse Accept-Language header to get preferred locale
 * Moved from page component to middleware for centralized logic
 */
function parseAcceptLanguage(acceptLanguage: string): SupportedLocale {
  const supportedLocales: SupportedLocale[] = ['en', 'pt', 'es'];

  try {
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const [langCode, weight] = lang.trim().split(';');
        const code = langCode?.substring(0, 2)?.toLowerCase() || '';
        const priority = weight ? parseFloat(weight.split('=')[1] || '1.0') : 1.0;
        return { code, priority };
      })
      .sort((a, b) => b.priority - a.priority);

    // Find the first supported language
    for (const lang of languages) {
      if (supportedLocales.includes(lang.code as SupportedLocale)) {
        return lang.code as SupportedLocale;
      }
    }
  } catch (error) {
    // If parsing fails, fall back to default
  }

  return 'en';
}

/**
 * Handle root URL redirects with Edge Runtime compatible locale detection
 * Uses browser Accept-Language header for locale detection since auth services
 * require Node.js APIs not available in Edge Runtime
 */
async function handleRootUrlRedirect(request: NextRequest, pathname: string): Promise<NextResponse> {
  let preferredLocale: SupportedLocale = 'en';

  // Check for locale cookie first (user preference)
  const cookieLocale = request.cookies.get('locale')?.value;
  if (cookieLocale && locales.includes(cookieLocale as any)) {
    preferredLocale = cookieLocale as SupportedLocale;
    
    if (process.env.NODE_ENV === 'development') {
      console.log('[middleware] Using cookie locale preference:', preferredLocale);
    }
  } else {
    // Use browser Accept-Language header detection
    const acceptLanguage = request.headers.get('accept-language');
    if (acceptLanguage) {
      preferredLocale = parseAcceptLanguage(acceptLanguage);
      
      if (process.env.NODE_ENV === 'development') {
        console.log('[middleware] Using browser locale detection:', {
          'accept-language': acceptLanguage,
          'detected-locale': preferredLocale
        });
      }
    }
  }

  // Set detected locale header for server components to use
  const response = NextResponse.redirect(new URL(`/${preferredLocale}${pathname === '/' ? '' : pathname}`, request.url));
  response.headers.set('x-detected-locale', preferredLocale);
  
  if (process.env.NODE_ENV === 'development') {
    console.log('[middleware] Redirecting to:', `/${preferredLocale}${pathname === '/' ? '' : pathname}`);
  }

  return response;
}



export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - images, icons (static assets)
     */
    '/((?!api|_next/static|_next/image|favicon.ico|images|icons|robots.txt|sitemap.xml|monitoring).*)',
  ],
};