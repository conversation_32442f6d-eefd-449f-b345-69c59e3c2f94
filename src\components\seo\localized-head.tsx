/**
 * SEO-optimized metadata utilities for App Router
 * Handles hreflang, localized metadata, and structured data using Metadata API
 */

import { Metadata } from 'next';
import { generateSEOMetadata } from '@/lib/i18n/server';

interface LocalizedMetadataProps {
  namespace: string;
  pageKey: string;
  additionalMetadata?: Partial<Metadata>;
  structuredData?: object;
}

/**
 * Generate localized metadata for App Router pages
 * Uses the new Metadata API instead of next/head
 */
export async function generateLocalizedMetadata({
  namespace,
  pageKey,
  additionalMetadata = {},
  structuredData,
}: LocalizedMetadataProps): Promise<Metadata> {
  const seoData = await generateSEOMetadata(namespace, pageKey);

  const metadata: Metadata = {
    title: seoData.title,
    description: seoData.description,
    openGraph: {
      title: seoData.ogTitle,
      description: seoData.ogDescription,
      locale: seoData.locale,
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: seoData.ogTitle,
      description: seoData.ogDescription,
    },
    alternates: {
      canonical: seoData.canonicalUrl,
      languages: Object.fromEntries(
        seoData.hreflangLinks.map(link => [link.hrefLang, link.href])
      ),
    },
    ...additionalMetadata,
  };

  return metadata;
}

/**
 * Generate structured data script for App Router
 * Can be used in layout.tsx or page.tsx files
 */
export function StructuredDataScript({ data }: { data: object }) {
  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(data),
      }}
    />
  );
}