/**
 * @fileoverview Therapeutic Properties Table component with expandable rows and oil enrichment.
 * Displays therapeutic properties in a table format with nested oil suggestions and safety data.
 */

'use client';

import React, { useState, memo, useCallback } from 'react';
import { cn } from '@/lib/utils';

// UI Components
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { SafetyStatusBadge } from '@/components/ui/safety-status-badge';
import { SafetyDetailsTabs } from '@/components/ui/safety-details-tabs';
import { RelevancyBadge } from '@/components/ui/relevancy-badge';
import { Progress } from '@/components/ui/progress';

// Icons
import {
  ChevronRight,
  ChevronUp,
  ChevronDown,
  Loader2,
  Info,
  AlertTriangle,
  Shield,
  Droplets,
  Sun,
  Baby
} from 'lucide-react';

// Feature imports
import { TherapeuticProperty, PropertyOilSuggestions, EnrichedEssentialOil } from '../types/recipe.types';
import { useRecipeStore } from '../store/recipe-store';
import { isEnrichedOil } from '../utils';
import { useCreateRecipeStreaming } from '../hooks/use-create-recipe-streaming';
import { useApiLanguage } from '@/lib/i18n/utils';
import { useAnimatedEllipsis } from '@/hooks/use-animated-ellipsis';
import { useI18n } from '@/hooks/use-i18n';

interface TherapeuticPropertiesTableProps {
  properties: TherapeuticProperty[];
  selectedCauses: any[];
  selectedSymptoms: any[];
  parallelStreamingState?: {
    isStreaming: boolean;
    results: Map<string, any>;
    errors: Map<string, string>;
  };
  propertyEnrichmentStatus: Record<string, 'idle' | 'loading' | 'success' | 'error'>;
  setPropertyEnrichmentStatus: (propertyId: string, status: 'idle' | 'loading' | 'success' | 'error') => void;
  updatePropertyWithEnrichedOils: (propertyId: string, enrichedOils: EnrichedEssentialOil[]) => void;
}

/**
 * Helper function to determine if a property is currently loading
 */
const usePropertyLoadingState = (parallelStreamingState?: {
  isStreaming: boolean;
  results: Map<string, any>;
  errors: Map<string, string>;
}) => {
  
  const isPropertyLoading = (propertyId: string): boolean => {
    if (!parallelStreamingState) return false;
    // Property is loading if:
    // 1. Parallel streaming is active AND
    // 2. Property ID is NOT in results Map yet AND
    // 3. Property ID is NOT in errors Map
    const isLoading = parallelStreamingState.isStreaming && 
           !parallelStreamingState.results.has(propertyId) && 
           !parallelStreamingState.errors.has(propertyId);
    
    if (parallelStreamingState.isStreaming) {
    }
    
    return isLoading;
  };
  
  const isPropertyCompleted = (propertyId: string): boolean => {
    if (!parallelStreamingState) return false;
    // Property is completed if it has results or errors
    return parallelStreamingState.results.has(propertyId) || 
           parallelStreamingState.errors.has(propertyId);
  };
  
  const isPropertyError = (propertyId: string): boolean => {
    if (!parallelStreamingState) return false;
    return parallelStreamingState.errors.has(propertyId);
  };
  
  return {
    isPropertyLoading,
    isPropertyCompleted,
    isPropertyError,
    isAnyStreaming: parallelStreamingState?.isStreaming || false
  };
};

/**
 * Individual oil table row component with expandable safety details
 */
const OilTableRow = React.memo(({ oil, isEnriched, t }: { 
  oil: EnrichedEssentialOil;
  isEnriched?: boolean; 
  t: ReturnType<typeof useI18n>["t"];
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const safetyData = isEnriched && oil.safety ? oil.safety : null;

  return (
    <React.Fragment>
      {/* Main oil row */}
      <TableRow className="group cursor-pointer hover:bg-accent/50 border-b border-border/30">
        <TableCell className="py-3 px-4">
          <div className="space-y-1">
            <div className="font-medium text-foreground">
              {oil.name_localized || oil.name_english}
            </div>
            {oil.name_scientific && (
              <div className="text-sm italic text-muted-foreground">
                {oil.name_scientific}
              </div>
            )}
            


            {oil.match_rationale_localized && (
              <div className="text-xs text-muted-foreground leading-relaxed">
                {oil.match_rationale_localized}
              </div>
            )}
          </div>
        </TableCell>
        
        <TableCell className="py-3 px-4">
          {oil.relevancy_to_property_score !== undefined && (
            <RelevancyBadge score={oil.relevancy_to_property_score} />
          )}
        </TableCell>
        
        <TableCell className="py-3 px-4">
          {safetyData && (
            <div className="flex flex-wrap gap-1">
              {safetyData.internal_use && (
                <SafetyStatusBadge 
                  category="internal" 
                  safety={safetyData.internal_use}
                  variant="compact"
                />
              )}
              {safetyData.dilution && (
                <SafetyStatusBadge 
                  category="dilution" 
                  safety={safetyData.dilution}
                  variant="compact"
                />
              )}
              {safetyData.phototoxicity && (
                <SafetyStatusBadge 
                  category="phototoxicity" 
                  safety={safetyData.phototoxicity}
                  variant="compact"
                />
              )}
              {safetyData.pregnancy_nursing && (
                <SafetyStatusBadge 
                  category="pregnancy" 
                  safety={safetyData.pregnancy_nursing}
                  variant="compact"
                />
              )}
              {safetyData.child_safety && safetyData.child_safety.length > 0 && (
                <SafetyStatusBadge 
                  category="child" 
                  safety={safetyData.child_safety}
                  variant="compact"
                />
              )}
            </div>
          )}
        </TableCell>
        
        <TableCell className="py-3 px-4">
          {safetyData && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setIsExpanded(!isExpanded)}
              className="h-8 px-2"
            >
              {isExpanded ? (
                <ChevronUp className="h-3 w-3" />
              ) : (
                <ChevronDown className="h-3 w-3" />
              )}
            </Button>
          )}
        </TableCell>
      </TableRow>
      
      {/* Expandable safety details row */}
      {safetyData && isExpanded && (
        <TableRow>
          <TableCell colSpan={4} className="p-0">
            <div className="p-4 bg-muted/20 border-t">
              <div className="space-y-3">
                <div className="flex items-center gap-2 mb-3">
                  <Info className="h-4 w-4 text-blue-600" />
                  <span className="font-medium text-sm">{t('create-recipe:therapeuticProperties.safetyInfo', 'Safety Information')}</span>
                </div>
                <SafetyDetailsTabs oil={oil} />
              </div>
            </div>
          </TableCell>
        </TableRow>
      )}
    </React.Fragment>
  );
});

OilTableRow.displayName = 'OilTableRow';

/**
 * Expanded row content showing suggested oils
 */
const ExpandedRowContent = ({ 
  property,
  propertyEnrichmentStatus,
  setPropertyEnrichmentStatus,
  updatePropertyWithEnrichedOils,
  t
}: { 
  property: TherapeuticProperty;
  propertyEnrichmentStatus: Record<string, 'idle' | 'loading' | 'success' | 'error'>;
  setPropertyEnrichmentStatus: (propertyId: string, status: 'idle' | 'loading' | 'success' | 'error') => void;
  updatePropertyWithEnrichedOils: (propertyId: string, enrichedOils: EnrichedEssentialOil[]) => void;
  t: ReturnType<typeof useI18n>["t"];
}) => {
  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h4 className="font-semibold text-lg text-foreground">{t('create-recipe:therapeuticProperties.suggestedOils', 'Suggested Essential Oils')}</h4>
        {/* The "Enrich" button from the reference file is intentionally omitted as per requirements. */}
      </div>
      
      {/* Display loading or error states based on the centralized enrichment status */}
      {propertyEnrichmentStatus[property.property_id] === 'loading' && (
        <div className="p-6 flex items-center text-muted-foreground">
          <Loader2 className="h-5 w-5 animate-spin mr-2" />
          <span>{t('create-recipe:therapeuticProperties.loading.enriching', 'Enriching oils with safety data...')}</span>
        </div>
      )}

      {propertyEnrichmentStatus[property.property_id] === 'error' && (
        <div className="p-6 text-destructive text-sm font-medium">
          {t('create-recipe:therapeuticProperties.error.safety', 'An error occurred while fetching safety data.')}
        </div>
      )}

      {/* Display the table only when the process is not loading or errored */}
      {(propertyEnrichmentStatus[property.property_id] === 'idle' || propertyEnrichmentStatus[property.property_id] === 'success') && (
        <div className="rounded-lg border border-border/50 bg-card/50 overflow-hidden shadow-sm">
          <Table>
            <TableHeader>
              <TableRow className="border-b border-border/50 bg-muted/30">
                <TableHead className="text-left font-semibold text-foreground py-3 px-4">
                  {t('create-recipe:therapeuticProperties.expandedTable.headers.essentialOil', 'Essential Oil')}
                </TableHead>
                <TableHead className="text-left font-semibold text-foreground py-3 px-4">
                  {t('create-recipe:therapeuticProperties.expandedTable.headers.match', 'Match')}
                </TableHead>
                <TableHead className="text-left font-semibold text-foreground py-3 px-4">
                  {t('create-recipe:therapeuticProperties.expandedTable.headers.safetyStatus', 'Safety Status')}
                </TableHead>
                <TableHead className="text-left font-semibold text-foreground py-3 px-4 w-24">
                  {t('create-recipe:therapeuticProperties.expandedTable.headers.details', 'Details')}
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {(!property.suggested_oils || property.suggested_oils.length === 0) ? (
                <TableRow>
                  <TableCell colSpan={4} className="p-6 text-sm text-center text-muted-foreground">
                    {t('create-recipe:therapeuticProperties.empty.noOils', 'No oil suggestions available for this property.')}
                  </TableCell>
                </TableRow>
              ) : (
                property.suggested_oils.map((oil) => (
                  <OilTableRow 
                    key={oil.oil_id} 
                    oil={oil} 
                    isEnriched={oil.isEnriched || false} 
                    t={t}
                  />
                ))
              )}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
};

/**
 * A new component to encapsulate the logic for a single property row.
 * This fixes the issue of having a div inside a tbody, which is invalid HTML.
 */
const PropertyRow = ({ 
  property, 
  selectedCauses, 
  selectedSymptoms, 
  parallelStreamingState,
  propertyEnrichmentStatus,
  setPropertyEnrichmentStatus,
  updatePropertyWithEnrichedOils,
  t
}: {
  property: TherapeuticProperty;
  selectedCauses: any[];
  selectedSymptoms: any[];
  parallelStreamingState?: TherapeuticPropertiesTableProps['parallelStreamingState'];
  propertyEnrichmentStatus: Record<string, 'idle' | 'loading' | 'success' | 'error'>;
  setPropertyEnrichmentStatus: (propertyId: string, status: 'idle' | 'loading' | 'success' | 'error') => void;
  updatePropertyWithEnrichedOils: (propertyId: string, enrichedOils: EnrichedEssentialOil[]) => void;
  t: ReturnType<typeof useI18n>["t"];
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const { isPropertyLoading } = usePropertyLoadingState(parallelStreamingState);
  const ellipsis = useAnimatedEllipsis();

  const oilCount = property.suggested_oils?.length || 0;
  const relevancyScore = property.relevancy_score || property.relevancy || 0;
  const isLoading = isPropertyLoading(property.property_id);
  const hasOils = (property.suggested_oils?.length ?? 0) > 0;
  const canExpand = hasOils;

  const getAddressedCauses = (prop: TherapeuticProperty) => {
    if (!prop.addresses_cause_ids?.length) return [];
    return selectedCauses.filter(cause => 
      prop.addresses_cause_ids?.includes(cause.cause_id)
    );
  };
  
  const getAddressedSymptoms = (prop: TherapeuticProperty) => {
    if (!prop.addresses_symptom_ids?.length) return [];
    return selectedSymptoms.filter(symptom => 
      prop.addresses_symptom_ids?.includes(symptom.symptom_id)
    );
  };

  const addressedCauses = getAddressedCauses(property);
  const addressedSymptoms = getAddressedSymptoms(property);

  return (
    <React.Fragment>
      <TableRow 
        className={cn(
          'border-b border-border/30 last:border-b-0',
          canExpand && 'cursor-pointer',
          isExpanded && 'bg-accent/20',
        )}
        onClick={() => canExpand && setIsExpanded(!isExpanded)}
      >
        {/* Property Info Cell */}
        <TableCell className="py-4 px-6 align-middle">
          <div className="space-y-2">
            <div className="font-medium text-foreground group-hover:text-primary transition-colors duration-200 flex items-center gap-2">
              {property.property_name_localized || property.property_name}
              {isLoading && (
                <Loader2 className="h-4 w-4 animate-spin text-blue-500" />
              )}
            </div>
            <div className="text-sm text-muted-foreground leading-relaxed">
              {property.description_contextual_localized || property.description || 'No description available'}
            </div>
            {(addressedCauses.length > 0 || addressedSymptoms.length > 0) && (
              <div className="mt-3 pt-3 border-t border-border/30">
                <div className="space-y-2">
                  {addressedCauses.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {addressedCauses.map((cause, i) => (
                        <div key={`${cause.cause_id}-${i}`} className="inline-flex items-center rounded-full border px-3 py-1.5 text-xs font-medium transition-colors bg-muted border-border text-chart-2 hover:bg-chart-2/10 hover:border-chart-2/30" title={`Cause: ${cause.cause_name}`}>
                          <div className="w-1.5 h-1.5 rounded-full bg-chart-2 mr-2" />
                          {cause.cause_name}
                        </div>
                      ))}
                    </div>
                  )}
                  {addressedSymptoms.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {addressedSymptoms.map((symptom, i) => (
                        <div key={`${symptom.symptom_id}-${i}`} className="inline-flex items-center rounded-full border px-3 py-1.5 text-xs font-medium transition-colors bg-muted border-border text-chart-3 hover:bg-chart-3/10 hover:border-chart-3/30" title={`Symptom: ${symptom.symptom_name}`}>
                          <div className="w-1.5 h-1.5 rounded-full bg-chart-3 mr-2" />
                          {symptom.symptom_name}
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </TableCell>

        {/* Relevancy Cell */}
        <TableCell className="py-4 px-4 text-center align-middle">
          <RelevancyBadge score={relevancyScore} />
        </TableCell>
        
        {/* Oils Count Cell */}
        <TableCell className="py-4 px-4 text-center align-middle">
          {isLoading ? (
            <span className="text-sm text-muted-foreground font-medium">{ellipsis}</span>
          ) : (
            <Badge variant="outline" className="font-medium">
              {oilCount}
            </Badge>
          )}
        </TableCell>
        
        {/* Details/Chevron Cell */}
        <TableCell className="py-4 px-4 text-center align-middle">
          {canExpand ? (
            <ChevronRight className={cn("h-4 w-4 text-muted-foreground transition-transform duration-200", isExpanded && "rotate-90")} />
          ) : (
            <div className="h-4 w-4" />
          )}
        </TableCell>
      </TableRow>

      {/* Collapsible Content Row */}
      <Collapsible asChild open={isExpanded}>
        <CollapsibleContent asChild>
          <TableRow>
            <TableCell colSpan={4} className="p-0">
              <div className="p-4 bg-muted/20 border-t border-border/30">
                <ExpandedRowContent 
                  property={property}
                  propertyEnrichmentStatus={propertyEnrichmentStatus}
                  setPropertyEnrichmentStatus={setPropertyEnrichmentStatus}
                  updatePropertyWithEnrichedOils={updatePropertyWithEnrichedOils}
                  t={t}
                />
              </div>
            </TableCell>
          </TableRow>
        </CollapsibleContent>
      </Collapsible>
    </React.Fragment>
  );
};

/**
 * Main Therapeutic Properties Table Component
 */
export function TherapeuticPropertiesTable({
  properties,
  selectedCauses,
  selectedSymptoms,
  parallelStreamingState,
  propertyEnrichmentStatus,
  setPropertyEnrichmentStatus,
  updatePropertyWithEnrichedOils
}: TherapeuticPropertiesTableProps) {
  const { t } = useI18n();
  /**
   * Individual property loading state detection
   */
  const { isPropertyLoading } = usePropertyLoadingState(parallelStreamingState);
  
  /**
   * Animated ellipsis for loading states
   */
  const ellipsis = useAnimatedEllipsis();

  if (!properties || properties.length === 0) {
    return (
      <div className="text-center py-12 space-y-6">
        <div className="space-y-2">
          <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto" />
          <h3 className="text-lg font-semibold text-muted-foreground">No Properties Found</h3>
          <p className="text-muted-foreground max-w-md mx-auto">
            No therapeutic properties available. Please go back to previous steps.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div data-testid="therapeutic-properties-table" className="space-y-4">
      {/* Expandable Properties Table */}
      <div className="rounded-lg border border-border/50 bg-card/50 backdrop-blur-sm overflow-hidden shadow-sm">
        <Table>
          <TableHeader>
            <TableRow className="border-b border-border/50 bg-muted/30">
              <TableHead className="font-semibold text-foreground py-4 px-6 w-auto">
                {t('create-recipe:therapeuticProperties.mainTable.headers.therapeuticProperties', 'Therapeutic Properties ({count})', { count: properties.length })}
              </TableHead>
              <TableHead className="font-semibold text-foreground py-4 px-4 w-32 text-center">
                {t('create-recipe:therapeuticProperties.mainTable.headers.relevancy', 'Relevancy')}
              </TableHead>
              <TableHead className="font-semibold text-foreground py-4 px-4 w-24 text-center">
                {t('create-recipe:therapeuticProperties.mainTable.headers.oils', 'Oils ({count})', { count: properties.reduce((total, prop) => total + (prop.suggested_oils?.length || 0), 0) })}
              </TableHead>
              <TableHead className="font-semibold text-foreground py-4 px-4 w-16 text-center">
                {t('create-recipe:therapeuticProperties.mainTable.headers.details', 'Details')}
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {properties.map((property) => (
              <PropertyRow
                key={property.property_id}
                property={property}
                selectedCauses={selectedCauses}
                selectedSymptoms={selectedSymptoms}
                parallelStreamingState={parallelStreamingState}
                propertyEnrichmentStatus={propertyEnrichmentStatus}
                setPropertyEnrichmentStatus={setPropertyEnrichmentStatus}
                updatePropertyWithEnrichedOils={updatePropertyWithEnrichedOils}
                t={t}
              />
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}