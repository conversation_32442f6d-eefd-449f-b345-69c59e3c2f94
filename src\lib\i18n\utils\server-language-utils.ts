/**
 * Server-side language detection utilities
 * Provides locale detection for Next.js server components and API routes
 */

import { headers, cookies } from 'next/headers';
import type { SupportedLocale } from '@/lib/i18n';

/**
 * Maps a user's language code to the API language code format
 * @param userLanguage The user's language code (e.g., 'pt', 'en')
 * @returns The corresponding API language code (e.g., 'PT_BR', 'EN_US')
 */
const LANGUAGE_MAP: Record<string, string> = {
  en: 'EN_US',
  pt: 'PT_BR',
  es: 'ES_ES',
  fr: 'FR_FR',
  de: 'DE_DE',
  it: 'IT_IT',
  // Add more language mappings as needed
};

// Default fallback language code
const DEFAULT_LANGUAGE: SupportedLocale = 'en';

// Default fallback API language code
const DEFAULT_API_LANGUAGE = 'EN_US';

/**
 * Gets the API language code from a user language code
 * @param userLanguage - ISO 639-1 language code (e.g., 'en', 'pt')
 * @returns API language code (e.g., 'EN_US', 'PT_BR')
 */
export function getApiLanguage(userLanguage?: string | null): string {
  if (!userLanguage) return DEFAULT_API_LANGUAGE;
  return LANGUAGE_MAP[userLanguage.toLowerCase()] || DEFAULT_API_LANGUAGE;
}

/**
 * Server-side locale detection from headers and cookies
 * Follows the same pattern as src/i18n/request.ts
 * @returns ISO 639-1 language code (e.g., 'en', 'pt', 'es')
 */
export async function getServerLocale(): Promise<SupportedLocale> {
  try {
    // Get headers and cookies
    const headersList = await headers();
    const cookieStore = await cookies();
    
    // Priority order:
    // 1. x-locale header (set by middleware or client)
    // 2. locale cookie (user preference)
    // 3. accept-language header (browser preference)
    // 4. default fallback
    
    // Check x-locale header first (highest priority)
    const localeHeader = headersList.get('x-locale');
    if (localeHeader && isValidLocale(localeHeader)) {
      return localeHeader as SupportedLocale;
    }
    
    // Check locale cookie
    const localeCookie = cookieStore.get('locale')?.value;
    if (localeCookie && isValidLocale(localeCookie)) {
      return localeCookie as SupportedLocale;
    }
    
    // Check accept-language header
    const acceptLanguage = headersList.get('accept-language');
    if (acceptLanguage) {
      const preferredLocale = parseAcceptLanguage(acceptLanguage);
      if (preferredLocale && isValidLocale(preferredLocale)) {
        return preferredLocale as SupportedLocale;
      }
    }
    
    // Fallback to default
    return DEFAULT_LANGUAGE;
  } catch (error) {
    console.warn('Error detecting server locale, using default:', error);
    return DEFAULT_LANGUAGE;
  }
}

/**
 * Parse Accept-Language header to extract preferred locale
 * @param acceptLanguage - Accept-Language header value
 * @returns Preferred locale or null
 */
function parseAcceptLanguage(acceptLanguage: string): string | null {
  try {
    // Parse "en-US,en;q=0.9,pt;q=0.8" format
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const [locale, qValue] = lang.trim().split(';');
        const quality = qValue ? parseFloat(qValue.split('=')[1]) : 1.0;
        return { locale: locale.split('-')[0].toLowerCase(), quality };
      })
      .sort((a, b) => b.quality - a.quality);
    
    // Return the highest quality supported language
    for (const { locale } of languages) {
      if (isValidLocale(locale)) {
        return locale;
      }
    }
    
    return null;
  } catch (error) {
    console.warn('Error parsing Accept-Language header:', error);
    return null;
  }
}

/**
 * Check if a locale is supported
 * @param locale - Locale to check
 * @returns True if locale is supported
 */
function isValidLocale(locale: string): boolean {
  return ['en', 'pt', 'es'].includes(locale.toLowerCase());
}

/**
 * Get localized metadata for page titles and descriptions
 * @param locale - Target locale
 * @returns Localized metadata object
 */
export function getLocalizedMetadata(locale: SupportedLocale = 'en') {
  const metadata = {
    en: {
      recipeCreator: 'Recipe Creator',
      createRecipe: 'Create Recipe',
      description: 'Create your personalized essential oil recipe',
      stepNotFound: 'Recipe Creator - Step Not Found',
      stepNotFoundDescription: 'The requested recipe creation step was not found.'
    },
    pt: {
      recipeCreator: 'Criador de Receitas',
      createRecipe: 'Criar Receita',
      description: 'Crie sua receita personalizada de óleos essenciais',
      stepNotFound: 'Criador de Receitas - Etapa Não Encontrada',
      stepNotFoundDescription: 'A etapa de criação de receita solicitada não foi encontrada.'
    },
    es: {
      recipeCreator: 'Creador de Recetas',
      createRecipe: 'Crear Receta',
      description: 'Crea tu receta personalizada de aceites esenciales',
      stepNotFound: 'Creador de Recetas - Paso No Encontrado',
      stepNotFoundDescription: 'El paso de creación de receta solicitado no fue encontrado.'
    }
  };
  
  return metadata[locale] || metadata.en;
}

/**
 * Get the API language code using server-side locale detection
 * @returns API language code (e.g., 'EN_US', 'PT_BR')
 */
export async function getServerApiLanguage(): Promise<string> {
  const locale = await getServerLocale();
  return getApiLanguage(locale);
}
