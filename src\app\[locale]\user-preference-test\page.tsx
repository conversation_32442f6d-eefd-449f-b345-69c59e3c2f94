/**
 * Test page demonstrating user preference integration with server-only i18n
 * Shows how URL locale and user database preferences work together
 */

import { 
  createTranslatorWithUserPreference, 
  getOptimalLocale,
  getLocaleFromParams 
} from '@/lib/i18n/server';
import { getCurrentUserProfile } from '@/features/user-auth-data/services/profile.service';
import { getServerAuthState } from '@/features/auth/services/auth-state.service';
import { Metadata } from 'next';
import Link from 'next/link';

interface UserPreferenceTestPageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: UserPreferenceTestPageProps): Promise<Metadata> {
  const { locale } = await params;
  
  return {
    title: `User Preference i18n Test - ${locale.toUpperCase()}`,
    description: 'Test page demonstrating user preference integration with server-only i18n system',
  };
}

export default async function UserPreferenceTestPage({ params }: UserPreferenceTestPageProps) {
  const { locale } = await params;
  
  // Get user info for demonstration
  let user = null;
  let userProfile = null;
  try {
    const authState = await getServerAuthState();
    user = authState.user;
    if (user?.id) {
      userProfile = await getCurrentUserProfile(user.id);
    }
  } catch (error) {
    // Handle gracefully for non-authenticated users
  }

  // Demonstrate different locale detection methods
  const urlLocale = getLocaleFromParams(locale);
  const optimalLocale = await getOptimalLocale(locale);
  const t = await createTranslatorWithUserPreference('i18n-test', locale);

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-4xl font-bold mb-6">
          {t('title')} - User Preference Integration
        </h1>
        <p className="text-lg text-gray-600 mb-8">
          This page demonstrates how the server-only i18n system integrates with user language preferences stored in the database.
        </p>

        {/* Locale Detection Analysis */}
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-semibold text-blue-800 mb-4">
            Locale Detection Analysis
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
              <strong>URL Locale:</strong> {urlLocale}
            </div>
            <div>
              <strong>Optimal Locale:</strong> {optimalLocale}
            </div>
            <div>
              <strong>User Authenticated:</strong> {user ? 'Yes' : 'No'}
            </div>
            <div>
              <strong>User DB Language:</strong> {userProfile?.language || 'N/A'}
            </div>
          </div>
        </div>

        {/* User Status */}
        {user ? (
          <div className="bg-green-50 border border-green-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-green-800 mb-4">
              ✅ Authenticated User
            </h2>
            <div className="space-y-2 text-green-700">
              <p><strong>User ID:</strong> {user.id.substring(0, 8)}...</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Database Language Preference:</strong> {userProfile?.language || 'Not set'}</p>
              <p><strong>Current Page Locale:</strong> {optimalLocale}</p>
            </div>
            
            <div className="mt-4 p-4 bg-green-100 rounded">
              <h3 className="font-semibold mb-2">How it works for authenticated users:</h3>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li><strong>URL locale takes priority</strong> - If you visit /pt/, /es/, or /en/, that locale is used</li>
                <li><strong>User preference as fallback</strong> - If no URL locale, your saved language preference ({userProfile?.language || 'none'}) is used</li>
                <li><strong>Browser detection</strong> - If no user preference, browser language is detected</li>
                <li><strong>Default fallback</strong> - Finally falls back to English</li>
              </ol>
            </div>
          </div>
        ) : (
          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-6 mb-8">
            <h2 className="text-xl font-semibold text-yellow-800 mb-4">
              👤 Non-Authenticated User
            </h2>
            <div className="space-y-2 text-yellow-700">
              <p>You are not currently logged in.</p>
              <p><strong>Current Page Locale:</strong> {optimalLocale}</p>
            </div>
            
            <div className="mt-4 p-4 bg-yellow-100 rounded">
              <h3 className="font-semibold mb-2">How it works for non-authenticated users:</h3>
              <ol className="list-decimal list-inside space-y-1 text-sm">
                <li><strong>URL locale takes priority</strong> - If you visit /pt/, /es/, or /en/, that locale is used</li>
                <li><strong>Browser detection</strong> - If no URL locale, browser Accept-Language header is used</li>
                <li><strong>Default fallback</strong> - Finally falls back to English</li>
              </ol>
            </div>
          </div>
        )}

        {/* Feature Demonstration */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-3">
              {t('features.server-only.title')}
            </h3>
            <p className="text-gray-600">
              {t('features.server-only.description')}
            </p>
          </div>
          <div className="p-6 border rounded-lg">
            <h3 className="text-lg font-semibold mb-3">
              {t('features.no-fouc.title')}
            </h3>
            <p className="text-gray-600">
              {t('features.no-fouc.description')}
            </p>
          </div>
        </div>

        {/* Test Different Scenarios */}
        <div className="bg-purple-50 border border-purple-200 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-semibold text-purple-800 mb-3">
            Test Different Scenarios
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Link 
              href="/en/user-preference-test" 
              className={`px-4 py-2 rounded transition-colors text-center ${
                optimalLocale === 'en' 
                  ? 'bg-purple-600 text-white' 
                  : 'bg-purple-500 text-white hover:bg-purple-600'
              }`}
            >
              Force English
            </Link>
            <Link 
              href="/pt/user-preference-test" 
              className={`px-4 py-2 rounded transition-colors text-center ${
                optimalLocale === 'pt' 
                  ? 'bg-green-600 text-white' 
                  : 'bg-green-500 text-white hover:bg-green-600'
              }`}
            >
              Force Portuguese
            </Link>
            <Link 
              href="/es/user-preference-test" 
              className={`px-4 py-2 rounded transition-colors text-center ${
                optimalLocale === 'es' 
                  ? 'bg-red-600 text-white' 
                  : 'bg-red-500 text-white hover:bg-red-600'
              }`}
            >
              Force Spanish
            </Link>
          </div>
          
          <div className="mt-4 text-sm text-purple-700">
            <p><strong>Note:</strong> URL locale always takes priority over user preferences for SEO and direct access.</p>
          </div>
        </div>

        {/* Navigation */}
        <div className="flex flex-wrap gap-4">
          <Link 
            href={`/${optimalLocale}/i18n-test`}
            className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
          >
            Basic i18n Test
          </Link>
          <Link 
            href={`/${optimalLocale}/examples`}
            className="px-4 py-2 bg-green-500 text-white rounded hover:bg-green-600 transition-colors"
          >
            Client Component Examples
          </Link>
          <Link 
            href={`/${optimalLocale}`}
            className="px-4 py-2 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors"
          >
            Back to Home
          </Link>
        </div>

        <div className="mt-8 text-sm text-gray-500">
          <p>
            <strong>Technical Note:</strong> This page uses <code>createTranslatorWithUserPreference()</code> 
            and <code>getOptimalLocale()</code> to demonstrate the enhanced server-only i18n system with user preference integration.
          </p>
        </div>
      </div>
    </div>
  );
}

export async function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'pt' },
    { locale: 'es' },
  ];
}
