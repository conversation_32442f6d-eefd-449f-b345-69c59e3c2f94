'use client';

import { useAuthSession } from '@/providers/auth-session-provider';
import { useUserProfileQuery } from '@/features/user-auth-data/hooks/use-user-profile-query';
import { useMemo, useEffect, useRef, useCallback } from 'react';
import * as Sentry from '@sentry/nextjs';
import { type User } from '@supabase/supabase-js';
import { type UserProfile } from '@/features/user-auth-data/schemas';

interface AuthState {
  user: User | null;
  profile: UserProfile | null | undefined;
  authUser: any; // Simplified to avoid complex type intersection issues
  isAuthenticated: boolean;
  isLoading: boolean;
  error: Error | null;
  profileError: Error | null;
  // Performance monitoring
  _performance?: {
    renderCount: number;
    lastRenderTime: number;
  };
  // Error recovery
  retry: () => void;
  clearErrors: () => void;
}

// Stable reference for performance tracking
const createPerformanceTracker = () => {
  let renderCount = 0;
  let lastRenderTime = 0;
  
  return {
    track: () => {
      renderCount++;
      lastRenderTime = Date.now();
      return { renderCount, lastRenderTime };
    },
    reset: () => {
      renderCount = 0;
      lastRenderTime = 0;
    }
  };
};

// Global performance tracker (stable reference)
const performanceTracker = createPerformanceTracker();

/**
 * Primary hook for authentication state
 * Combines session and profile data with advanced optimizations:
 * - React.memo for return value stability
 * - Error recovery mechanisms
 * - Performance monitoring
 * - Connection resilience
 */
export function useAuth(): AuthState {
  // Debug flag for conditional logging - only log state changes, not every render
  const debugAuth = process.env.NODE_ENV === 'development' && 
                    process.env['NEXT_PUBLIC_DEBUG_AUTH'] === 'true';
  
  // Performance tracking
  const performanceRef = useRef(performanceTracker.track());
  
  // Track previous state to only log actual changes
  const previousStateRef = useRef<string>('');
  
  // Error recovery state
  const retryCountRef = useRef(0);
  const maxRetries = 3;
  const retryTimeoutRef = useRef<NodeJS.Timeout>();
  
  // Get raw session data
  const { 
    user: sessionUser, 
    isLoading: isSessionLoading, 
    error: sessionError 
  } = useAuthSession();
  
  // Only fetch profile if we have a user
  const { 
    data: profileData, 
    isLoading: isProfileLoading,
    error: profileError,
    refetch: refetchProfile
  } = useUserProfileQuery(
    sessionUser?.id,
    { 
      enabled: !!sessionUser?.id,
      // Enhanced error recovery with exponential backoff
      retry: (failureCount: number, error: any) => {
        if (failureCount < maxRetries) {
          const delay = Math.min(1000 * Math.pow(2, failureCount), 10000);
          if (debugAuth) {
            console.log(`[useAuth] Retrying profile fetch in ${delay}ms (attempt ${failureCount + 1}/${maxRetries})`);
          }
          return true;
        }
        return false;
      },
      retryDelay: (attemptIndex: number) => Math.min(1000 * Math.pow(2, attemptIndex), 10000),
      // Stale-while-revalidate pattern for better UX
      staleTime: 5 * 60 * 1000, // 5 minutes
      cacheTime: 10 * 60 * 1000, // 10 minutes
      refetchOnWindowFocus: false,
      refetchOnReconnect: true, // Refetch when connection is restored
    }
  );
  
  // Error recovery function
  const retry = useCallback(() => {
    if (retryCountRef.current < maxRetries) {
      retryCountRef.current++;
      
      // Clear any existing timeout
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
      
      // Retry with exponential backoff
      const delay = Math.min(1000 * Math.pow(2, retryCountRef.current - 1), 10000);
      
      retryTimeoutRef.current = setTimeout(() => {
        if (sessionUser?.id && profileError) {
          refetchProfile();
        }
      }, delay);
      
      if (debugAuth) {
        console.log(`[useAuth] Retry scheduled in ${delay}ms (attempt ${retryCountRef.current}/${maxRetries})`);
      }
    }
  }, [sessionUser?.id, profileError, refetchProfile, debugAuth]);
  
  // Clear errors function
  const clearErrors = useCallback(() => {
    retryCountRef.current = 0;
    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }
  }, []);
  
  // Reset retry count on successful data fetch
  useEffect(() => {
    if (profileData && !profileError) {
      retryCountRef.current = 0;
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    }
  }, [profileData, profileError]);
  
  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);
  
  // Report significant errors to Sentry with enhanced context
  useEffect(() => {
    if (sessionError) {
      // Dev-only console logging
      if (debugAuth) {
        console.error('[useAuth] Session error:', sessionError.message);
      }
      
      // Always report to Sentry with enhanced context
      Sentry.captureException(sessionError, {
        tags: { 
          component: 'useAuth', 
          type: 'sessionError',
          retryCount: retryCountRef.current
        },
        extra: { 
          userId: sessionUser?.id ? `${sessionUser.id.substring(0, 6)}...` : 'none',
          operation: 'useAuth',
          message: "Error from AuthSessionProvider",
          performance: performanceRef.current,
          hasRetried: retryCountRef.current > 0
        }
      });
    }
  }, [sessionError, sessionUser?.id, debugAuth]);
  
  useEffect(() => {
    if (profileError && sessionUser) {
      // Dev-only console logging
      if (debugAuth) {
        console.error('[useAuth] Profile error:', profileError);
      }
      
      // Always report to Sentry with enhanced context
      Sentry.captureException(profileError, {
        tags: { 
          component: 'useAuth', 
          type: 'profileError',
          retryCount: retryCountRef.current
        },
        extra: { 
          userId: `${sessionUser.id.substring(0, 6)}...`,
          operation: 'useAuth',
          message: "Error from useUserProfileQuery",
          performance: performanceRef.current,
          hasRetried: retryCountRef.current > 0
        }
      });
    }
  }, [profileError, sessionUser?.id, debugAuth]);
  
  // Memoize derived states with enhanced performance tracking
  return useMemo(() => {
    // Update performance tracking
    performanceRef.current = performanceTracker.track();
    
    const isAuthenticated = !!sessionUser && !isSessionLoading;
    const isLoading = isSessionLoading || (isAuthenticated && isProfileLoading);
    
    // Combined user object with both session and profile data
    const authUser = sessionUser && profileData ? {
      ...sessionUser,
      ...profileData,
    } : null;
    
    // Create stable return object
    const authState: AuthState = {
      user: sessionUser,
      profile: profileData,
      authUser,
      isAuthenticated,
      isLoading,
      error: sessionError,
      profileError,
      _performance: debugAuth ? performanceRef.current : undefined,
      retry,
      clearErrors
    };
    
    return authState;
  }, [
    sessionUser?.id, // Only depend on user ID, not the whole user object
    profileData?.id, // Only depend on profile ID, not the whole profile object
    isSessionLoading, 
    isProfileLoading, 
    sessionError?.message, // Only depend on error message, not the whole error object
    profileError?.message,
    debugAuth,
    retry,
    clearErrors
  ]);
}
