# i18n Component Migration Design

## Overview

This design outlines the migration from our current wrapper-based i18n approach to next-intl, eliminating boilerplate wrapper components and leveraging Next.js App Router patterns. We'll replace custom i18n hooks with industry-standard next-intl patterns while maintaining all existing functionality.

## Architecture

### Current Problem Analysis
We're currently using a wrapper-based approach that creates maintenance overhead:
- ❌ **Wrapper Components**: Files like `homepage-layout-i18n.tsx` wrap every client component
- ❌ **Custom Hooks**: `use-i18n.ts` and `use-server-i18n.ts` create non-standard patterns
- ❌ **Boilerplate**: Every client component needs a corresponding wrapper
- ❌ **Performance**: Extra components in the tree and larger client bundles

### Target Architecture with next-intl
We'll migrate to industry-standard patterns:
- ✅ **NextIntlClientProvider**: Single provider in root layout
- ✅ **Standard Hooks**: `useTranslations` from next-intl for client components
- ✅ **Server Functions**: `getTranslations` from next-intl/server for server components
- ✅ **No Wrappers**: Direct translation usage in components

### Components Requiring Migration
Based on analysis, we need to identify and migrate:

#### Phase 1: Audit Wrapper Components
- Find all files matching pattern `*-i18n.tsx`
- Document which components they wrap
- Identify translation namespaces used

#### Phase 2: Component Categories
1. **Server Components**: Can use `getTranslations` directly
2. **Client Components**: Will use `useTranslations` with NextIntlClientProvider
3. **Complex Cases**: May need props-based translation passing

## Migration Patterns

### Pattern 1: Server Component (Preferred)
For components that can be server components:

```typescript
// Before: Wrapper approach
// homepage-layout-i18n.tsx
'use client';
import { useTranslations } from '@/hooks/use-i18n';
import HomepageLayout from './homepage-layout';

export default function HomepageLayoutI18n() {
  const t = useTranslations();
  return <HomepageLayout translations={t} />;
}

// After: Direct server component
// homepage-layout.tsx
import { useTranslations } from 'next-intl/server';

export default async function HomepageLayout() {
  const t = await useTranslations('homepage');
  return (
    <div>
      <h1>{t('title')}</h1>
      <p>{t('description')}</p>
    </div>
  );
}
```

### Pattern 2: Client Component with Context
For components that must be client components:

```typescript
// Before: Wrapper approach
// mobile-layout-i18n.tsx
'use client';
import { useTranslations } from '@/hooks/use-i18n';
import MobileLayout from './mobile-layout';

export default function MobileLayoutI18n() {
  const t = useTranslations();
  return <MobileLayout translations={t} />;
}

// After: Direct client component
// mobile-layout.tsx
'use client';
import { useTranslations } from 'next-intl';

export default function MobileLayout() {
  const t = useTranslations('create-recipe');
  return (
    <div>
      <h1>{t('navigation.title')}</h1>
      <button>{t('navigation.buttons.next')}</button>
    </div>
  );
}
```

### Pattern 3: Props-Based (For Complex Cases)
When you need more control over translations:

```typescript
// page.tsx (Server Component)
import { getTranslations } from 'next-intl/server';
import ComplexComponent from './complex-component';

export default async function Page() {
  const t = await getTranslations('create-recipe');
  return (
    <ComplexComponent 
      translations={{
        title: t('title'),
        description: t('description'),
        buttons: {
          next: t('navigation.buttons.next'),
          previous: t('navigation.buttons.previous')
        }
      }}
    />
  );
}

// complex-component.tsx (Client Component)
'use client';

interface Props {
  translations: {
    title: string;
    description: string;
    buttons: {
      next: string;
      previous: string;
    };
  };
}

export default function ComplexComponent({ translations }: Props) {
  return (
    <div>
      <h1>{translations.title}</h1>
      <p>{translations.description}</p>
      <button>{translations.buttons.next}</button>
    </div>
  );
}
```

## Setup Requirements

### next-intl Installation and Configuration

#### Package Installation
```bash
npm install next-intl
```

#### Root Layout Configuration
```typescript
// app/layout.tsx
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';

export default async function RootLayout({ 
  children, 
  params: { locale } 
}: {
  children: React.ReactNode;
  params: { locale: string };
}) {
  const messages = await getMessages();
  
  return (
    <html lang={locale}>
      <body>
        <NextIntlClientProvider messages={messages}>
          {children}
        </NextIntlClientProvider>
      </body>
    </html>
  );
}
```

#### Middleware Configuration
Update existing middleware to work with next-intl patterns for locale handling.

## Component Migration Strategy

### Wrapper Component Elimination
Priority order for removing wrapper components:

1. **Identify All Wrappers**: Find files matching `*-i18n.tsx` pattern
2. **Analyze Component Type**: Determine if wrapped component can be server or must be client
3. **Choose Migration Pattern**: Apply appropriate pattern from above
4. **Update Imports**: Replace wrapper imports with direct component imports
5. **Delete Wrapper**: Remove the `-i18n.tsx` file
6. **Test**: Verify functionality in all locales

## Hook Migration Strategy

### Replacing use-i18n.ts
- Replace all `useTranslations()` calls from custom hook with `useTranslations()` from next-intl
- Update namespace usage to match next-intl patterns
- Remove custom hook file after migration complete
- Ensure client components have access to NextIntlClientProvider

### Replacing use-server-i18n.ts
- Replace server-side usage with `getTranslations()` from next-intl/server
- Update async patterns to match next-intl server functions
- Remove custom server hook after migration complete
- Maintain proper TypeScript types with next-intl types

## Translation Integration with next-intl

### Translation File Structure
Existing translation files will work with next-intl:

```json
// messages/en/create-recipe.json
{
  "navigation": {
    "breadcrumb": {
      "ariaLabel": "Recipe creation progress",
      "progress": "Progress"
    },
    "buttons": {
      "previous": "Previous",
      "next": "Next"
    }
  },
  "wizard": {
    "status": { "loading": "Loading..." }
  }
}
```

### next-intl Translation Usage
Components will use next-intl patterns:
- `t('navigation.progress')` for progress indicators (within create-recipe namespace)
- `t('navigation.buttons.next')` for navigation buttons
- `t('login.title')` for authentication messages (within auth namespace)
- `t('buttons.retry')` for common UI elements (within common namespace)

## Performance Considerations

### Server-Side Rendering Benefits
- Translations loaded server-side eliminate client-side loading
- No flash of untranslated content (FOUC)
- Better Core Web Vitals scores
- Improved SEO with fully translated content

### Caching Strategy
- Leverage next-intl's built-in caching mechanisms
- Translation loading is optimized by next-intl
- Server-side translations are cached per request automatically

### Bundle Size Optimization
- Remove client-side translation loading code
- Reduce JavaScript bundle size
- Eliminate client-side locale detection logic

## Error Handling Strategy

### Translation Fallbacks
- Use existing fallback system in centralized utilities
- Missing keys fall back to key name
- Missing files fall back to English
- Graceful degradation for all scenarios

### Component Error Handling
- Maintain existing error boundary patterns
- Ensure error messages are translated
- Provide fallback UI for translation failures

## Testing Strategy

### Component Testing
- Test each component in all supported locales (en, pt, es)
- Verify translation key resolution
- Test variable interpolation
- Validate fallback behavior

### Integration Testing
- Test parent-child translation passing
- Verify server-side rendering works correctly
- Test hydration without issues
- Validate performance improvements

### Manual Testing
- Test user workflows in all languages
- Verify no functionality regression
- Test error scenarios
- Validate loading states

## Migration Sequence

### Phase 1: Utility Cleanup (Low Risk)
1. Remove duplicate `parseAcceptLanguage` functions
2. Update utility imports
3. Clean up legacy code patterns

### Phase 2: Simple Components (Medium Risk)
1. Migrate `loading-skeletons.tsx`
2. Migrate `error-boundary.tsx`
3. Test basic functionality

### Phase 3: Complex Components (Higher Risk)
1. Migrate `mobile-layout.tsx`
2. Migrate `auth-guard.tsx`
3. Update parent components
4. Comprehensive testing

### Phase 4: Hook Updates (Medium Risk)
1. Update `use-i18n.ts`
2. Update `use-server-i18n.ts`
3. Test hook compatibility

### Phase 5: Validation (Low Risk)
1. End-to-end testing
2. Performance validation
3. SEO verification
4. Documentation updates

## Risk Mitigation

### High Risk Areas
- Complex component dependencies
- Client-side interactivity requirements
- Translation key mapping accuracy

### Mitigation Strategies
- Use reference implementation as guide
- Incremental migration with testing
- Maintain backward compatibility
- Comprehensive fallback handling

### Rollback Plan
- Keep original implementations until validation complete
- Use feature flags if needed
- Document all changes for easy reversal

## Success Criteria

### Functional Requirements
- All components render correctly in all locales
- No functionality regression
- Proper error handling and fallbacks
- Variable interpolation works correctly

### Performance Requirements
- No client-side hydration issues
- Improved Core Web Vitals scores
- Faster initial page loads
- No flash of untranslated content

### Code Quality Requirements
- No duplicate i18n logic
- Proper TypeScript types
- Clean component interfaces
- Maintainable code structure

This design provides a comprehensive plan for migrating all components to next-intl while eliminating wrapper components and improving performance.