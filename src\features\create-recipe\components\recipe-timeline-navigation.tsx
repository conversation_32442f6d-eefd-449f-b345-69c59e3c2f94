/**
 * @fileoverview Timeline navigation component for Final Recipes
 * Shows interactive timeline with dots and connecting lines for time slots
 */

'use client';

import React from 'react';
import { RecipeTimeSlot } from '../types/recipe.types';
import { getTimeSlotConfig, getTimeSlots } from '../constants/time-slot-config';
import { useI18n } from '@/hooks/use-i18n';

interface RecipeTimelineNavigationProps {
  activeProtocol: RecipeTimeSlot;
  onProtocolChange: (protocol: RecipeTimeSlot) => void;
  completedProtocols?: Set<RecipeTimeSlot>;
}

/**
 * Timeline navigation component following the designer's reference
 * Shows dots with connecting lines and time-specific styling
 */
export const RecipeTimelineNavigation = React.memo(function RecipeTimelineNavigation({
  activeProtocol,
  onProtocolChange,
  completedProtocols = new Set()
}: RecipeTimelineNavigationProps) {
  const { t } = useI18n();
  const timeSlots = getTimeSlots();

  return (
    <div className="relative py-8">
      {/* Timeline container */}
      <div className="timeline relative">
        {/* Timeline line */}
        <div className="timeline-line absolute left-4 top-6 bottom-6 w-0.5 bg-border"></div>
        
        {/* Timeline items */}
        <div className="space-y-6">
          {timeSlots.map((timeSlot, index) => {
            const config = getTimeSlotConfig(timeSlot);
            const isActive = activeProtocol === timeSlot;
            const isCompleted = completedProtocols.has(timeSlot);
            const translationKey = timeSlot === 'mid-day' ? 'midDay' : timeSlot;
            
            return (
              <div
                key={timeSlot}
                className={`timeline-item relative pl-12 cursor-pointer transition-all duration-300 ${
                  isActive ? 'active-box' : ''
                }`}
                onClick={() => onProtocolChange(timeSlot)}
              >
                {/* Timeline dot */}
                <div
                  className={`dot absolute left-4 top-6 transform -translate-x-1/2 w-5 h-5 rounded-full border-3 transition-all duration-300 ${
                    isActive
                      ? 'border-primary bg-primary'
                      : isCompleted
                      ? 'border-primary/60 bg-primary/60'
                      : 'border-border bg-background'
                  }`}
                />
                
                {/* Protocol card */}
                <div
                  className={`bg-card rounded-xl p-4 border transition-all duration-300 hover:shadow-md ${
                    isActive
                      ? 'border-primary/50 shadow-lg shadow-primary/10 bg-primary/5'
                      : 'border-border hover:border-primary/30'
                  }`}
                >
                  <div className="flex items-center gap-3">
                    <span className="text-2xl">{config.emoji}</span>
                    <div className="flex-1">
                      <h3 className={`font-semibold transition-colors ${
                        isActive ? 'text-primary' : 'text-card-foreground'
                      }`}>
                        {t(`create-recipe:steps.final-recipes.protocols.${translationKey}.label`)}
                      </h3>
                      <p className="text-sm text-muted-foreground">
                        {config.timeRange}
                      </p>
                      <p className="text-xs text-muted-foreground mt-1">
                        {t(`create-recipe:steps.final-recipes.protocols.${translationKey}.purpose`)}
                      </p>
                    </div>
                    
                    {/* Status indicator */}
                    <div className="flex items-center gap-2">
                      {isCompleted && (
                        <div className="w-2 h-2 rounded-full bg-primary animate-pulse"></div>
                      )}
                      {isActive && (
                        <div className="w-3 h-3 rounded-full bg-primary"></div>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
});