/**
 * @fileoverview Modern AI Chat-style Health Concern Input component for Essential Oil Recipe Creator.
 * Provides a clean, minimal interface similar to modern AI chat interfaces with centered layout,
 * prominent title, and elegant input design matching the visual examples provided.
 */

'use client';

import React, { useState, useRef, useCallback, useEffect } from 'react';
import { Textarea } from '@/components/ui/textarea';
import { cn } from '@/lib/utils';
import { ArrowUpIcon, Heart } from 'lucide-react';
import { useRecipeStore } from '../store/recipe-store';
import { useRecipeWizardNavigation } from '../hooks/use-recipe-navigation';
import { useI18n } from '@/hooks/use-i18n';

/**
 * Helper hook for auto-resizing textarea
 */
function useAutoResizeTextarea({
  minHeight,
  maxHeight,
}: { minHeight: number; maxHeight?: number }) {
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const adjustHeight = useCallback(
    (reset?: boolean) => {
      const textarea = textareaRef.current;
      if (!textarea) return;
      if (reset) {
        textarea.style.height = `${minHeight}px`;
        return;
      }
      textarea.style.height = `${minHeight}px`; // Reset first to get scrollHeight correctly
      const newHeight = Math.max(
        minHeight,
        Math.min(textarea.scrollHeight, maxHeight ?? Number.POSITIVE_INFINITY)
      );
      textarea.style.height = `${newHeight}px`;
    },
    [minHeight, maxHeight]
  );

  useEffect(() => {
    const textarea = textareaRef.current;
    if (textarea) textarea.style.height = `${minHeight}px`;
  }, [minHeight]);

  useEffect(() => {
    const handleResize = () => adjustHeight(); // Adjust on window resize
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, [adjustHeight]);

  return { textareaRef, adjustHeight };
}

/**
 * Chat-style Health Concern Input component
 */
export function HealthConcernChatInput() {
  const { t } = useI18n();

  const {
    healthConcern,
    updateHealthConcern,
    isLoading
  } = useRecipeStore();
  const { goToNext, canGoNext, markCurrentStepCompleted } = useRecipeWizardNavigation();

  const [inputValue, setInputValue] = useState(healthConcern?.healthConcern || '');
  const [isSaving, setIsSaving] = useState(false);

  const { textareaRef, adjustHeight } = useAutoResizeTextarea({
    minHeight: 60,
    maxHeight: 200,
  });

  // Example texts for translation
  const examples = [
    t('create-recipe:chatInput.examples.headaches'),
    t('create-recipe:chatInput.examples.digestive'),
    t('create-recipe:chatInput.examples.sleep'),
    t('create-recipe:chatInput.examples.tension')
  ];

  /**
   * Auto-save functionality with debouncing
   */
  const autoSave = useCallback(async (value: string) => {
    if (value.trim().length < 3) return;

    setIsSaving(true);
    try {
      updateHealthConcern({ healthConcern: value });

      // Mark step as completed if valid
      if (value.trim().length >= 3) {
        markCurrentStepCompleted();
      }
    } catch (error) {
      console.error('Auto-save failed:', error);
    } finally {
      setIsSaving(false);
    }
  }, [updateHealthConcern, markCurrentStepCompleted]);

  /**
   * Debounced auto-save effect
   */
  useEffect(() => {
    if (!inputValue || inputValue.trim().length < 3) return;

    const timeoutId = setTimeout(() => {
      autoSave(inputValue);
    }, 1000); // 1 second debounce

    return () => clearTimeout(timeoutId);
  }, [inputValue, autoSave]);

  /**
   * Handle input change
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    setInputValue(e.target.value);
    adjustHeight();
  };

  /**
   * Handle form submission
   */
  const handleSubmit = async () => {
    if (inputValue.trim().length < 3 || isLoading) return;

    try {
      updateHealthConcern({ healthConcern: inputValue });
      markCurrentStepCompleted();

      if (canGoNext()) {
        await goToNext();
      }
    } catch (error) {
      console.error('Form submission failed:', error);
    }
  };

  /**
   * Handle key down events
   */
  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit();
    }
  };

  const characterCount = inputValue?.length || 0;
  const isValid = characterCount >= 3 && characterCount <= 500;
  const isNearLimit = characterCount > 450;
  const isAtLimit = characterCount >= 500;

  return (
    <div className="min-h-screen bg-background flex flex-col items-center justify-center p-6">
      {/* Header - Simplified */}
      <div className="text-center mb-12 max-w-2xl">
        <div className="flex items-center justify-center gap-3 mb-4">
          <Heart className="w-7 h-7 text-primary" />
          <h1 className="text-2xl font-semibold text-foreground">
            {t('create-recipe:chatInput.title')}
          </h1>
        </div>
        <p className="text-base text-muted-foreground/80 leading-relaxed">
          {t('create-recipe:chatInput.description')}
        </p>
      </div>

      {/* Main Input */}
      <div className="w-full max-w-2xl mb-6">
        <div className="relative bg-card border border-border rounded-2xl shadow-sm hover:shadow-md transition-shadow">
          <Textarea
            ref={textareaRef}
            value={inputValue}
            onChange={handleInputChange}
            onKeyDown={handleKeyDown}
            placeholder={t('create-recipe:chatInput.placeholder')}
            className={cn(
              "w-full resize-none bg-transparent border-none px-4 py-3",
              "text-foreground text-base leading-relaxed",
              "focus:outline-none focus-visible:ring-0 focus-visible:ring-offset-0",
              "placeholder:text-muted-foreground/60",
              "min-h-[52px] max-h-[120px]"
            )}
            style={{ overflow: "hidden" }}
            disabled={isLoading}
            maxLength={500}
          />

          {/* Bottom bar with counter and submit */}
          <div className="flex items-center justify-between px-4 py-2 border-t border-border/50">
            <div className="flex items-center gap-3 text-xs text-muted-foreground">
              {isSaving && (
                <div className="flex items-center gap-1.5">
                  <div className="animate-spin rounded-full h-3 w-3 border border-primary border-t-transparent"></div>
                  <span>{t('common:status.saving')}</span>
                </div>
              )}
              <span className={cn(
                "transition-colors",
                isNearLimit && "text-orange-500",
                isAtLimit && "text-destructive"
              )}>
                {characterCount}/500
              </span>
            </div>

            <button
              type="button"
              className={cn(
                "p-2 rounded-lg transition-all duration-200 flex items-center justify-center",
                isValid
                  ? "bg-primary text-primary-foreground hover:bg-primary/90"
                  : "bg-muted/50 text-muted-foreground cursor-not-allowed"
              )}
              disabled={!isValid || isLoading}
              onClick={handleSubmit}
              aria-label={t('common:buttons.continue')}
            >
              <ArrowUpIcon className="w-4 h-4" />
            </button>
          </div>
        </div>
      </div>

      {/* Example Suggestions - Improved chips */}
      <div className="w-full max-w-3xl">
        <p className="text-sm text-muted-foreground/70 mb-3 text-center">
          {t('create-recipe:chatInput.subtitle')}
        </p>
        <p className="text-xs text-muted-foreground/60 mb-4 text-center">
          Ou escolha um exemplo:
        </p>
        <div className="flex flex-wrap justify-center gap-2">
          {examples.map((example, index) => (
            <button
              key={index}
              onClick={() => {
                setInputValue(example);
                adjustHeight();
              }}
              className="inline-flex items-center px-4 py-2 bg-card/60 hover:bg-card border border-border/40 hover:border-border rounded-full text-sm text-muted-foreground hover:text-foreground transition-all duration-200 hover:shadow-sm"
            >
              {example}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
}
