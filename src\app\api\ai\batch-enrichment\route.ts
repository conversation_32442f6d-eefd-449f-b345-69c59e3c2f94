import { NextRequest } from 'next/server';
import { batchEnrichOils, type SuggestedOilData } from '@/lib/ai/services/batch-enrichment.service';
import { 
  validateOpenAIKey,
  parseJsonBody, 
  createErrorResponse, 
  createSuccessResponse,
  withErrorHandling
} from '@/lib/ai/utils/api-helpers';
import { FileLogger } from '@/lib/debug/file-logger';

async function handler(request: NextRequest) {
  const traceId = `batch-enrichment-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
  const inputLogger = new FileLogger({ logDirectory: 'input', fileName: `batch-enrichment-input-${traceId}.json`, mode: 'transactional' });
  const finalOutputLogger = new FileLogger({ logDirectory: 'output', fileName: `batch-enrichment-final-${traceId}.json`, mode: 'transactional' });

  try {
    validateOpenAIKey();
  
    const body = await parseJsonBody(request);
    await inputLogger.log(body);

    const { suggestedOils } = body;

    if (!suggestedOils || !Array.isArray(suggestedOils)) {
      const errorMessage = { error: '`suggestedOils` array is required in the request body.' };
      await finalOutputLogger.log(errorMessage);
      return createErrorResponse(errorMessage.error, 400);
    }

    console.log(`🔍 [API Route] Received batch enrichment request for ${suggestedOils.length} oils.`);

    const result = await batchEnrichOils(suggestedOils as SuggestedOilData[]);

    console.log(`✅ [API Route] Batch enrichment successful.`, {
      enriched: result.total_enriched,
      notFound: result.total_not_found,
      discarded: result.total_discarded,
      processingTime: `${result.processing_time_ms}ms`
    });

    // Log the raw result data before wrapping it in a response
    await finalOutputLogger.log({
      data: result,
      timestamp: new Date().toISOString(),
      status: 'success'
    });

    return createSuccessResponse(result);
  } catch (error: unknown) {
    const errorMessage = {
      error: error instanceof Error ? error.message : 'Unknown error occurred',
      timestamp: new Date().toISOString(),
      status: 'error'
    };
    await finalOutputLogger.log(errorMessage);
    return createErrorResponse(errorMessage.error, 500);
  }
}

export const POST = withErrorHandling(handler); 