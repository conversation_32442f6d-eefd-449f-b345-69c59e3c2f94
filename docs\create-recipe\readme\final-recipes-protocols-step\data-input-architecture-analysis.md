# Data Input Architecture Analysis - Final Recipe Step

## Overview

This document provides a comprehensive technical analysis of how user data flows from previous wizard steps into the final recipe generation step. The analysis traces the complete data flow from initial user input through state management to AI processing.

## Data Sources Feeding into Final Recipe Generation

### 1. Health Concern Data (`healthConcern`)

**Source**: Step 1 - Health Concern Form  
**File**: `src/features/create-recipe/components/health-concern-form.tsx`  
**Store Action**: `updateHealthConcern()`  
**Data Structure**:
```typescript
interface HealthConcernData {
  healthConcern: string;
}
```

**Data Flow**:
1. User inputs health concern in text field
2. Input is sanitized using `sanitizeHealthConcern()` utility
3. Data stored in Zustand store via `updateHealthConcern()`
4. Accessed in final recipes via `useRecipeStore()` selector

**Validation**: 
- Maximum 500 characters
- Special characters filtered except basic punctuation
- Multiple spaces collapsed to single space

### 2. Demographics Data (`demographics`)

**Source**: Step 2 - Demographics Form  
**File**: `src/features/create-recipe/components/demographics-form.tsx`  
**Store Action**: `updateDemographics()`  
**Data Structure**:
```typescript
interface DemographicsData {
  gender: 'male' | 'female';
  ageCategory: string;
  specificAge: number;
}
```

**Data Flow**:
1. User selects gender and age category
2. Age validation against category using `validateAgeCategory()`
3. Data stored via `updateDemographics()`
4. Used for safety filtering in final recipes

**Age Categories** (from `recipe.constants.ts`):
- `0-2`: 0-2 years
- `3-5`: 3-5 years  
- `6-11`: 6-11 years
- `12-17`: 12-17 years
- `18-64`: 18-64 years
- `65+`: 65+ years

### 3. Selected Causes Data (`selectedCauses`)

**Source**: Step 3 - Causes Selection  
**File**: `src/features/create-recipe/components/causes-selection.tsx`  
**Store Action**: `updateSelectedCauses()`  
**Data Structure**:
```typescript
interface PotentialCause {
  cause_id: string; // AI-generated UUID
  cause_name: string;
  cause_suggestion: string;
  explanation: string;
}
```

**Data Flow**:
1. AI generates potential causes based on health concern + demographics
2. User selects relevant causes from AI-generated list
3. Selection stored via `updateSelectedCauses()`
4. Clearing causes also clears dependent data (symptoms, properties, oils)

**AI Generation**: Uses `potential-causes.yaml` prompt with health concern and demographics

### 4. Selected Symptoms Data (`selectedSymptoms`)

**Source**: Step 4 - Symptoms Selection  
**File**: `src/features/create-recipe/components/symptoms-selection.tsx`  
**Store Action**: `updateSelectedSymptoms()`  
**Data Structure**:
```typescript
interface PotentialSymptom {
  symptom_id: string; // AI-generated UUID
  symptom_name: string;
  symptom_suggestion: string;
  explanation: string;
}
```

**Data Flow**:
1. AI generates potential symptoms based on health concern + demographics + selected causes
2. User selects relevant symptoms from AI-generated list
3. Selection stored via `updateSelectedSymptoms()`
4. Clearing symptoms also clears dependent data (properties, oils)

**AI Generation**: Uses `potential-symptoms.yaml` prompt with previous step data

### 5. Therapeutic Properties Data (`therapeuticProperties`)

**Source**: Step 5 - Properties Display  
**File**: `src/features/create-recipe/components/properties-display.tsx`  
**Store Action**: `updateTherapeuticProperties()`  
**Data Structure**:
```typescript
interface TherapeuticProperty {
  property_id: string;
  property_name_localized: string;
  property_name_english: string;
  description_contextual_localized: string;
  addresses_cause_ids: string[];
  addresses_symptom_ids: string[];
  relevancy_score: number;
  suggested_oils?: EnrichedEssentialOil[];
  isLoadingOils?: boolean;
  errorLoadingOils?: string | null;
  isEnriched: boolean;
}
```

**Critical Data Flow Discovery**:
The analysis reveals that **`therapeuticProperties` is the primary data source** for final recipes, NOT the `suggestedOils` array. This is confirmed by the component code:

```typescript
// CRITICAL: Final Recipes uses therapeuticProperties directly
const hasTherapeuticPropertiesWithOils = therapeuticProperties.some(prop =>
  prop.isEnriched && prop.suggested_oils && prop.suggested_oils.length > 0
);

// The suggestedOils array is problematic and often empty
if (hasTherapeuticPropertiesWithOils && suggestedOils.length === 0) {
  console.log('❌ [Final Recipes] ISSUE CONFIRMED: therapeuticProperties has oils but suggestedOils array is empty!');
}
```

**Oil Enrichment Process**:
1. AI generates therapeutic properties based on causes + symptoms
2. Each property gets oil suggestions via parallel streaming
3. Oils are enriched with safety data from Supabase database
4. Properties marked as `isEnriched: true` when all oils processed
5. Final recipes extracts oils from enriched properties

### 6. Data Extraction for Final Recipes

**Critical Implementation**: Final recipes creates a minimal data structure from `therapeuticProperties`:

```typescript
// CREATE PROPERTY OIL SUGGESTIONS STRUCTURE (same format as debug overlay expects)
const propertyOilSuggestions = therapeuticProperties
  .filter(prop => prop.isEnriched && prop.suggested_oils && prop.suggested_oils.length > 0)
  .map(prop => ({
    property_id: prop.property_id,
    property_name_localized: prop.property_name_localized,
    property_name_english: prop.property_name_english,
    description_contextual_localized: prop.description_contextual_localized,
    suggested_oils: prop.suggested_oils || [],
    isEnriched: prop.isEnriched
  }));
```

## State Management Architecture

### Zustand Store Structure

**File**: `src/features/create-recipe/store/recipe-store.ts`

**Key State Properties**:
```typescript
interface RecipeWizardState {
  // Input data from previous steps
  healthConcern: HealthConcernData | null;
  demographics: DemographicsData | null;
  selectedCauses: PotentialCause[];
  selectedSymptoms: PotentialSymptom[];
  therapeuticProperties: TherapeuticProperty[];
  suggestedOils: PropertyOilSuggestions[]; // Problematic - often empty
  
  // Final recipes state
  finalRecipes: FinalRecipesState;
  isStreamingFinalRecipes: boolean;
}
```

### Data Validation and Sanitization

**Health Concern Sanitization** (`api-data-transform.ts`):
```typescript
export function sanitizeHealthConcern(input: string): string {
  return input
    .trim()
    .replace(/\s+/g, ' ') // Replace multiple spaces with single space
    .replace(/[^\w\s\-.,!?]/g, '') // Remove special characters except basic punctuation
    .substring(0, 500); // Ensure max length
}
```

**Age Validation**:
```typescript
export function validateAgeCategory(age: number, ageCategory: string): boolean {
  const category = AGE_CATEGORY_OPTIONS.find(cat => cat.value === ageCategory);
  if (!category) return false;
  return age >= category.minAge && age <= category.maxAge;
}
```

**Navigation Validation**:
```typescript
case RecipeStep.FINAL_RECIPES:
  const hasBasicData = !!state.healthConcern && !!state.demographics &&
         state.selectedCauses.length > 0 && state.selectedSymptoms.length > 0;
  const hasProperties = state.therapeuticProperties.length > 0;
  const hasEnrichedProperties = state.therapeuticProperties.some(p => p.isEnriched);
  return hasBasicData && hasProperties && hasEnrichedProperties;
```

## Data Transformation Pipeline

### 1. Input Collection Phase

**Trigger**: Component mount with required data  
**Validation Logic**:
```typescript
const hasTherapeuticPropertiesWithOils = therapeuticProperties.some(prop =>
  prop.isEnriched && prop.suggested_oils && prop.suggested_oils.length > 0
);

const hasRequiredData = healthConcern && demographics &&
  selectedCauses.length > 0 && selectedSymptoms.length > 0 &&
  hasTherapeuticPropertiesWithOils;
```

### 2. Data Preparation Phase

**Oil Extraction Process**:
```typescript
// Extract all unique oils from therapeuticProperties
const oilMap = new Map();
therapeuticProperties.forEach(prop => {
  (prop.suggested_oils || []).forEach(oil => {
    if (oil?.oil_id && !oilMap.has(oil.oil_id)) {
      // Only include essential oil fields (remove enrichment metadata)
      const { isEnriched, enrichment_status, botanical_mismatch, 
              similarity_score, search_query, enrichment_timestamp, 
              name_botanical, match_rationale_localized, 
              relevancy_to_property_score, ...oilRest } = oil;
      oilMap.set(oil.oil_id, { ...oilRest });
    }
  });
});
```

### 3. API Request Formation

**Stream Request Creation** (`api-data-transform.ts`):
```typescript
export function createStreamRequest(
  feature: 'create-recipe',
  step: 'final-recipes',
  healthConcern: HealthConcernData,
  demographics: DemographicsData,
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[],
  userLanguage: string,
  property?: undefined,
  additionalData: { timeSlot: RecipeTimeSlot, suggestedOils: PropertyOilSuggestions[] }
): StreamRequest
```

**Request Data Structure**:
```typescript
{
  feature: 'create-recipe',
  step: 'final-recipes',
  data: {
    health_concern: string,
    gender: 'male' | 'female',
    age_category: string,
    age_specific: string,
    user_language: string,
    selected_causes: PotentialCause[],
    selected_symptoms: PotentialSymptom[],
    time_of_day: 'morning' | 'mid-day' | 'night',
    suggested_oils: EssentialOil[] // Extracted from therapeuticProperties
  }
}
```

## Data Dependencies and Clearing Logic

### Dependency Chain

1. **Health Concern** → Demographics
2. **Demographics** → Causes  
3. **Causes** → Symptoms
4. **Symptoms** → Properties
5. **Properties** → Final Recipes

### Clearing Logic (`clearStepsAfter`)

**When navigating backwards**, dependent data is automatically cleared:

```typescript
switch (currentStep) {
  case RecipeStep.HEALTH_CONCERN:
    // Clear everything except health concern
    updates.demographics = null;
    updates.selectedCauses = [];
    updates.selectedSymptoms = [];
    updates.therapeuticProperties = [];
    updates.suggestedOils = [];
    break;
    
  case RecipeStep.DEMOGRAPHICS:
    // Clear causes and everything after
    updates.selectedCauses = [];
    updates.selectedSymptoms = [];
    updates.therapeuticProperties = [];
    updates.suggestedOils = [];
    break;
    
  // ... additional cases
}
```

## Critical Findings

### 1. Primary Data Source Issue

**Problem**: The `suggestedOils` array is often empty, but `therapeuticProperties` contains the actual oil data.

**Solution**: Final recipes component correctly uses `therapeuticProperties` directly:
```typescript
// CORRECT: Use therapeuticProperties as primary source
const propertyOilSuggestions = therapeuticProperties
  .filter(prop => prop.isEnriched && prop.suggested_oils && prop.suggested_oils.length > 0)
  .map(prop => ({ /* extract data */ }));
```

### 2. State Management Optimization

**Batched Updates**: Store uses optimized update patterns to minimize re-renders:
```typescript
// Only update if data actually changed
if (state.currentStep === step) return state;
```

### 3. Data Persistence Strategy

**Non-Persistent State**: Data is intentionally not persisted to localStorage:
```typescript
// Data is intentionally not persisted so browser refresh clears all state
export const useRecipeStore = create<RecipeStore>()((set, get) => ({
  ...initialState,
  // No persistence middleware
}));
```

## Technical Summary

The final recipe step receives data through a well-structured pipeline:

1. **Input Collection**: 6 previous wizard steps collect user data
2. **State Management**: Zustand store maintains data with dependency validation  
3. **Data Transformation**: `therapeuticProperties` serves as primary data source
4. **API Integration**: Data transformed into streaming requests for 3 time slots
5. **Safety Integration**: Demographics data enables age-appropriate safety filtering

**Key Files**:
- **`final-recipes-display.tsx`**: Main component with data validation logic
- **`recipe-store.ts`**: State management with navigation validation
- **`api-data-transform.ts`**: Data transformation utilities
- **`final-recipes.yaml`**: AI prompt configuration for recipe generation

**Critical Dependencies**:
- **`therapeuticProperties`**: Primary data source (NOT `suggestedOils`)
- **`demographics`**: Required for safety filtering
- **`healthConcern`**: Base context for all AI processing
- **`selectedCauses`** + **`selectedSymptoms`**: Symptom targeting data