import { notFound } from 'next/navigation';
import { getRequestConfig } from 'next-intl/server';

// Can be imported from a shared config
const locales = ['en', 'pt', 'es'];

export default getRequestConfig(async ({ locale }) => {
  // Validate that the incoming `locale` parameter is valid
  if (!locales.includes(locale as any)) notFound();

  // Load all translation files for the locale
  const [
    auth,
    common,
    createRecipe,
    dashboard,
    examples,
    homepage,
    i18nTest
  ] = await Promise.all([
    import(`./messages/${locale}/auth.json`),
    import(`./messages/${locale}/common.json`),
    import(`./messages/${locale}/create-recipe.json`),
    import(`./messages/${locale}/dashboard.json`),
    import(`./messages/${locale}/examples.json`),
    import(`./messages/${locale}/homepage.json`),
    import(`./messages/${locale}/i18n-test.json`)
  ]);

  return {
    messages: {
      auth: auth.default,
      common: common.default,
      'create-recipe': createRecipe.default,
      dashboard: dashboard.default,
      examples: examples.default,
      homepage: homepage.default,
      'i18n-test': i18nTest.default
    }
  };
});