### Context
We have built a multi-step recipe creation workflow at `src/features/create-recipe` using OpenAI Agents JS SDK, with AI integration at `src/lib/ai`, vector search via `src/lib/pinecone`, and database integration through `src/lib/supabase`. We need to create a Product Requirements Document (PRD) for the final step that generates personalized essential oil recipes.

### Core Requirements

#### 1. Data Input Processing
- **Source**: Final step receives user data matching the structure in `docs/create-recipe/new-final-step/json-dataset-minimal.json`
- **Processing**: Transform collected user health profile, demographics, and preferences into AI-ready format

#### 2. AI Recipe Generation System
- **Technology**: Implement using OpenAI Agents JS SDK following existing patterns in `src/lib/ai`
- **Parallel Processing**: Generate 3 distinct recipes simultaneously:
  - Morning protocol/recipe
  - Mid-day protocol/recipe  
  - Night protocol/recipe
- **Safety Logic**: Filter out dermocaustic oils when demographics indicate child user
- **Prompt Template**: Base implementation on `docs/create-recipe/new-final-step/08 - synergistic-oils.yaml` and the other step prompts located at 'src\features\create-recipe\prompts'

#### 3. Dynamic Frontend Generation
- **UI Reference**: Follow design mockup in `docs/create-recipe/new-final-step/standalone-v1.html` but only uses our theme variables. Do not hardcode styles, colors that are custom.
- **Dynamic Schema**: AI generates JSON schema that dynamically creates frontend fields
- **Required Sections** (based on `docs/create-recipe/new-final-step/aa-aistudio_essential_oil_protocol.md` but check the  `docs/create-recipe/new-final-step/standalone-v1.html` file to see what other fields are necessary):
  - Container & Dispensing System recommendations
  - Usage modes (main and alternative)
  - Exact measurements and dilution ratios
  - Timing and frequency protocols
  - Check the `docs/create-recipe/new-final-step/standalone-v1.html` file to see what other fields are necessary

#### 4. Core User Stories (MVP)
- **Health Profile Analysis**: Display complete therapeutic strategy rationale
- **Precise Recipe Instructions**: Exact measurements for safe preparation
- **Professional Protocol**: Detailed usage instructions with timing
- **Container Guidance**: Specific container types and preparation methods
- **Root Cause Analysis**: Explanation of oil selection reasoning
- **Carrier Oil Information**: Complete carrier oil recommendations

#### 5. Technical Implementation Requirements
- **Integration**: Seamless continuation from existing create-recipe workflow, currently we have a Oils steps that will never be used. We can replace it with this final step.
- **State Management**: Maintain user session data through final step
- **Error Handling**: Graceful fallbacks for AI generation failures
- **Performance**: Parallel API calls with loading states. Check other steps that do parallel calling using openai-agents-js.
- **Responsive Design**: Mobile-first approach (90% mobile users)
- **i18n Compliance**: Full internationalization support for en/es/pt - Check 'src/lib/i18n/README.md' for implementation guide and verify with other steps that are fully internationalized

### Future Enhancement Roadmap (Post-MVP)

#### Phase 2 Features  
- **Holistic Wellness Tabs**:
  - Positive Affirmations
  - Complementary Practices (grounding, exercise, nutrition)
  - Lifestyle Modifications (sleep hygiene, screen time reduction)
- **Rationale**: Address root causes beyond essential oils

#### Phase 3 Features
- **Oil Substitution**: Reload button located by each oil for alternative oils with same properties/symptoms
- **Scientific Studies**: Integration with `docs/own-mcp` for botanical research
- **Oil Profiles**: Deep-link to comprehensive oil database via Hasura GraphQL
- **Interactive Calculator**: Real-time dilution ratio calculator

### Success Criteria
- 100% test coverage for AI integration and recipe generation
- Sub-3-second parallel recipe generation
- Mobile-responsive design with intuitive UX
- Complete i18n implementation
- Integration with existing create-recipe workflow without breaking changes

### Technical Constraints
- Must use OpenAI Agents JS SDK (no direct API calls)
- Follow existing codebase patterns and architecture
- Maintain backward compatibility with current workflow
- Implement using incremental changes


The text above was based on this text from our project director brainstorm:
###
Overview:

We have been creating a user interactive multi-step flow at 'src\features\create-recipe' together with openai-agents-js SDK and 'src\lib\ai' including vector search using 'src\lib\pinecone' and 'src\lib\supabase'

Now we need to wrap up this feature with a final step, but we need to create a PRD for this final screen(step)

A really shallow brainstorm of the required steps to accomplish the final solution is: 

1 - Get the data that is at the last step of the create-recipe multi step workflow, a sample is located at docs\create-recipe\new-final-step\json-dataset-minimal.json
2 - It will need to create a openai-agents-js agent to get that data and build a recipe to populate all the fields to the frontend.
3 - The agent needs to be fired 3 times, one for a morning recipe/protocol, one for mid-day recipe/protocol, and last for a night recipe/protocol. Those calls needs to be made in parallel.
4 - if the demographics show kid, it will not send to the AI oils that are dermocaustic,
5 - A mockup of the frontend last page of the multi-step flow is located at docs\create-recipe\new-final-step\standalone-v1.html, all this fields are dinamycally created by the AI prompt that generates a json schema dinamycally based on the dataset sent to the AI, which will have this structure docs\create-recipe\new-final-step\json-dataset-minimal.json
--- Fields to be created by the JSON SCHEMA, an example is located at 'docs\create-recipe\new-final-step\aa-aistudio_essential_oil_protocol.md'
--- ### 4.5 Container & Dispensing System - **Container Recommendations:** Size calculations based on usage frequency - **Dispensing Method:** Roller bottle, dropper, spray, or solid container
--- ### 8.1 User Comprehension Metrics
- **Recipe Understanding:** 90% of users can identify main oils and usage method
- **Safety Compliance:** 95% of users notice and understand safety warnings
- **Preparation Accuracy:** Users can calculate correct dilutions independently
- **Protocol Adherence:** Clear understanding of timing and frequency requirements

usage mode main, usage mode alternative

### 9.1 Core Professional User Stories
**As a user**, I want to see my complete health profile analysis so I understand the therapeutic strategy behind my recipe
**As a user**, I want a complete recipe with exact measurements so I can prepare it safely and accurately
**As a user**, I want detailed usage instructions with timing so I can follow a professional protocol
**As a user**, I want to know exactly what container to buy and how to prepare it so I can create the recipe properly

### 9.2 Advanced User Stories
**As a user**, I want to see the root cause analysis so I understand why certain oils were selected
**As a user**, I want carrier oil information

6 - There is a really basic draft of the AI prompt for this step in the file 'docs\create-recipe\new-final-step\08 - synergistic-oils.yaml'


For later updates of this final screen:
- The frontend will have a (reload) button by each essential oils list on the recipe, and when the user clicks it, it will populate with oils that were addressing the same cause, or where in the same property, or were addressing the same symptom. User stories: Usuario gostaria de buscar possivieis substituicoes do oleo (rankeamento e tambem estejam na mesma propriedade)
- The cientific studies tab would search for studies using 'docs\own-mcp' with essential oil botanical names, chemical compounts in order to find the relevant studies.
- If the user clicks the oil to see Complete Oil Profiles, it should direct the to the essential oil information tab (future development) which will get all the info from the database using graphapi from hasana overlayer on supabase.
- Interactive dilution calculator
- Extra tabs for Positivi Afirmations, Extra PRactices: Associate health concern with another healthy practices: grounding, exercise, eat healthy, avoid stimulant beverages such as coffee, reduce screen time, hour sleep higiene. REASONING: it is not just about oils and drugs from pharmacy, you have to manage other things that are not helping, and are probably the underlying causes.
###