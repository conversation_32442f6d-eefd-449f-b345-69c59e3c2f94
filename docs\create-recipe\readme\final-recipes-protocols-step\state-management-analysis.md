# State Management Implementation Patterns Analysis

## Overview

This document provides a comprehensive analysis of the state management implementation patterns used in the final recipe step, focusing on how data persists between wizard steps, state selectors, update mechanisms, and data transformation points.

## Zustand Store Architecture

### Core Store Structure

**File**: `src/features/create-recipe/store/recipe-store.ts`

The store implements a **non-persistent state pattern** for reset-on-refresh behavior:

```typescript
/**
 * Main recipe wizard store WITHOUT persistence for reset-on-refresh behavior
 * Data is intentionally not persisted so browser refresh clears all state
 */
export const useRecipeStore = create<RecipeStore>()((set, get) => ({
  ...initialState,
  // No persistence middleware - intentional design choice
}));
```

### State Interface Structure

```typescript
interface RecipeWizardState {
  // Step data - Core wizard progression data
  healthConcern: HealthConcernData | null;
  demographics: DemographicsData | null;
  selectedCauses: PotentialCause[];
  selectedSymptoms: PotentialSymptom[];
  therapeuticProperties: TherapeuticProperty[];
  suggestedOils: PropertyOilSuggestions[]; // Legacy - often empty
  finalRecipes: FinalRecipesState;

  // API response data - Temporary storage for AI responses
  potentialCauses: PotentialCause[];
  potentialSymptoms: PotentialSymptom[];

  // Navigation state - Wizard flow control
  currentStep: RecipeStep;
  completedSteps: RecipeStep[];

  // Loading and error states - UI state management
  isLoading: boolean;
  error: string | null;

  // AI Streaming states - Real-time processing indicators
  isStreamingCauses: boolean;
  isStreamingSymptoms: boolean;
  isStreamingProperties: boolean;
  isStreamingOils: boolean;
  isStreamingFinalRecipes: boolean;
  streamingError: string | null;

  // Oil enrichment states - Property-level processing tracking
  propertyEnrichmentStatus: Record<string, 'idle' | 'loading' | 'success' | 'error'>;

  // Auto-analysis state - Workflow automation
  shouldAutoAnalyzeProperties: boolean;

  // Metadata - Session tracking and debugging
  lastUpdated: Date;
  sessionId: string; // Generated UUID for session tracking
}
```

## Data Persistence Patterns

### 1. Non-Persistent Design Philosophy

**Intentional Design Choice**: Data is NOT persisted to localStorage:

```typescript
// Data is intentionally not persisted so browser refresh clears all state
export const useRecipeStore = create<RecipeStore>()((set, get) => ({
  ...initialState,
  // No persistence middleware
}));
```

**Benefits**:
- Fresh start on each session
- No stale data issues
- Privacy-focused (no local data retention)
- Simplified debugging (predictable initial state)

### 2. Session-Based State Management

**Session ID Generation**:
```typescript
function generateUUID(): string {
  if (typeof crypto !== 'undefined' && crypto.randomUUID) {
    return crypto.randomUUID();
  }
  // Fallback for environments without crypto.randomUUID
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    const r = Math.random() * 16 | 0;
    const v = c === 'x' ? r : (r & 0x3 | 0x8);
    return v.toString(16);
  });
}
```

**Session Tracking**:
```typescript
const initialState: Omit<RecipeWizardState, keyof RecipeWizardActions> = {
  // ... other state
  lastUpdated: new Date(),
  sessionId: generateUUID() // New session ID on each initialization
};
```

## State Update Mechanisms

### 1. Optimized Update Patterns

**Conditional Updates** - Only update if data actually changed:

```typescript
setCurrentStep: (step: RecipeStep) => {
  set((state) => {
    // Only update if step actually changed
    if (state.currentStep === step) return state;
    return {
      currentStep: step,
      lastUpdated: new Date()
    };
  });
},
```

**Batched Updates** - Minimize re-renders:

```typescript
batchUpdateStreamingState: (updates: {
  isStreamingCauses?: boolean;
  isStreamingSymptoms?: boolean;
  isStreamingProperties?: boolean;
  isStreamingOils?: boolean;
  streamingError?: string | null;
}) => {
  set((state) => {
    const newState = { ...state };
    let hasChanges = false;

    // Only update fields that have actually changed
    if (updates.isStreamingCauses !== undefined && state.isStreamingCauses !== updates.isStreamingCauses) {
      newState.isStreamingCauses = updates.isStreamingCauses;
      hasChanges = true;
    }
    // ... additional field checks

    // Only update lastUpdated if there were actual changes
    if (hasChanges) {
      newState.lastUpdated = new Date();
      return newState;
    }

    return state; // No changes, return existing state
  });
},
```

### 2. Dependency-Based Data Clearing

**Cascading Data Clearing** - When navigating backwards:

```typescript
clearStepsAfter: (currentStep: RecipeStep) => {
  set((state) => {
    const updates: Partial<RecipeWizardState> = {
      lastUpdated: new Date()
    };

    // Clear data based on which step we're going back to
    switch (currentStep) {
      case RecipeStep.HEALTH_CONCERN:
        // Clear everything except health concern
        updates.demographics = null;
        updates.selectedCauses = [];
        updates.selectedSymptoms = [];
        updates.therapeuticProperties = [];
        updates.suggestedOils = [];
        updates.potentialCauses = [];
        updates.potentialSymptoms = [];
        break;

      case RecipeStep.DEMOGRAPHICS:
        // Clear causes and everything after
        updates.selectedCauses = [];
        updates.selectedSymptoms = [];
        updates.therapeuticProperties = [];
        updates.suggestedOils = [];
        updates.potentialCauses = [];
        updates.potentialSymptoms = [];
        break;

      // ... additional cases
    }

    return { ...state, ...updates };
  });
},
```

### 3. Therapeutic Properties Update Pattern

**Critical Pattern**: Properties are updated with source tracking:

```typescript
updateTherapeuticProperties: (properties: TherapeuticProperty[], source: string) => {
  const currentState = get().therapeuticProperties;
  console.log('ACTION: updateTherapeuticProperties');
  console.log('PAYLOAD:', { properties, source });

  // Only keep canonical fields for each property
  const canonicalProperties = properties.map(newProp => {
    const existing = currentState.find(cp => cp.property_id === newProp.property_id);
    // If existing is enriched and has oils, keep it
    if (existing && existing.isEnriched && existing.suggested_oils && existing.suggested_oils.length > 0) {
      return existing;
    }
    // Otherwise, only keep canonical fields
    return {
      property_id: newProp.property_id,
      property_name_localized: newProp.property_name_localized,
      property_name_english: newProp.property_name_english,
      description_contextual_localized: newProp.description_contextual_localized,
      addresses_cause_ids: newProp.addresses_cause_ids || [],
      addresses_symptom_ids: newProp.addresses_symptom_ids || [],
      relevancy_score: newProp.relevancy_score,
      suggested_oils: newProp.suggested_oils || [],
      isLoadingOils: newProp.isLoadingOils ?? false,
      errorLoadingOils: newProp.errorLoadingOils ?? null,
      isEnriched: newProp.isEnriched ?? false,
    };
  });

  set(() => ({
    therapeuticProperties: canonicalProperties,
    // Clear dependent data when properties change
    suggestedOils: [],
    lastUpdated: new Date()
  }));
},
```

## State Selector Patterns

### 1. Granular Selectors

**Navigation-Specific Selector**:
```typescript
export const useRecipeNavigationStore = () => useRecipeStore((state) => ({
  currentStep: state.currentStep,
  completedSteps: state.completedSteps,
  setCurrentStep: state.setCurrentStep,
  markStepCompleted: state.markStepCompleted,
  canNavigateToStep: state.canNavigateToStep
}));
```

**Data-Specific Selector**:
```typescript
export const useRecipeData = () => useRecipeStore((state) => ({
  healthConcern: state.healthConcern,
  demographics: state.demographics,
  selectedCauses: state.selectedCauses,
  selectedSymptoms: state.selectedSymptoms,
  therapeuticProperties: state.therapeuticProperties,
  suggestedOils: state.suggestedOils
}));
```

**Streaming-Specific Selector**:
```typescript
export const useRecipeStreaming = () => useRecipeStore((state) => ({
  isStreamingCauses: state.isStreamingCauses,
  isStreamingSymptoms: state.isStreamingSymptoms,
  isStreamingProperties: state.isStreamingProperties,
  isStreamingOils: state.isStreamingOils,
  streamingError: state.streamingError,
  setStreamingCauses: state.setStreamingCauses,
  setStreamingSymptoms: state.setStreamingSymptoms,
  setStreamingProperties: state.setStreamingProperties,
  setStreamingOils: state.setStreamingOils,
  setStreamingError: state.setStreamingError,
  clearStreamingError: state.clearStreamingError
}));
```

### 2. Component-Level State Access

**Final Recipes Component Pattern**:
```typescript
// Store state - using the same data source as debug overlay
const {
  healthConcern,
  demographics,
  selectedCauses,
  selectedSymptoms,
  therapeuticProperties, // This is the main data source (same as debug overlay)
  suggestedOils, // This is the problematic array that's never populated
  finalRecipes,
  isStreamingFinalRecipes,
  setFinalRecipesGenerating // ADDED: Need this to set hasStartedGeneration flag
} = useRecipeStore();
```

## Navigation State Management

### 1. Step Validation Logic

**Navigation Validation Pattern**:
```typescript
canNavigateToStep: (step: RecipeStep): boolean => {
  const state = get();

  switch (step) {
    case RecipeStep.FINAL_RECIPES:
      // Can navigate to Final Recipes when all previous steps are completed
      // and therapeutic properties have been enriched with oils
      const hasBasicData = !!state.healthConcern && !!state.demographics &&
             state.selectedCauses.length > 0 && state.selectedSymptoms.length > 0;
      const hasProperties = state.therapeuticProperties.length > 0;
      const hasEnrichedProperties = state.therapeuticProperties.some(p => p.isEnriched);
      const canNavigateToFinalRecipes = hasBasicData && hasProperties && hasEnrichedProperties;

      return canNavigateToFinalRecipes;

    // ... other cases
  }
},
```

### 2. Step Completion Tracking

**Completion State Management**:
```typescript
markStepCompleted: (step: RecipeStep) => {
  set((state) => {
    // Only update if step isn't already completed
    if (state.completedSteps.includes(step)) return state;

    const completedSteps = [...state.completedSteps, step];
    return {
      completedSteps,
      lastUpdated: new Date()
    };
  });
},
```

## Data Transformation Points

### 1. API Request Transformation

**Stream Request Creation** (`api-data-transform.ts`):
```typescript
export function createStreamRequest(
  feature: string,
  step: string,
  healthConcern: HealthConcernData,
  demographics: DemographicsData,
  selectedCauses: PotentialCause[],
  selectedSymptoms: PotentialSymptom[],
  userLanguage: string = DEFAULT_API_LANGUAGE,
  property?: TherapeuticProperty,
  additionalData?: any
): StreamRequest
```

### 2. Component-Level Data Transformation

**Final Recipes Data Extraction**:
```typescript
// CREATE PROPERTY OIL SUGGESTIONS STRUCTURE (same format as debug overlay expects)
const propertyOilSuggestions = therapeuticProperties
  .filter(prop => prop.isEnriched && prop.suggested_oils && prop.suggested_oils.length > 0)
  .map(prop => ({
    property_id: prop.property_id,
    property_name_localized: prop.property_name_localized,
    property_name_english: prop.property_name_english,
    description_contextual_localized: prop.description_contextual_localized,
    suggested_oils: prop.suggested_oils || [],
    isEnriched: prop.isEnriched
  }));
```

### 3. Oil Enrichment Data Flow

**Property Oil Enrichment Pattern**:
```typescript
updatePropertyWithEnrichedOils: (propertyId: string, enrichedOils: EnrichedEssentialOil[]) => {
  set((state) => {
    const updatedProperties = state.therapeuticProperties.map(p => {
      if (p.property_id === propertyId) {
        // Consider an oil enriched if it has been processed (has an enrichment_status)
        const updatedOils = enrichedOils.map(oil => ({
          ...oil,
          isEnriched: !!oil.enrichment_status // Oil is enriched if it has been processed
        }));

        // Property is enriched if all oils have been processed
        const allOilsProcessed = updatedOils.every(oil => !!oil.enrichment_status);

        return {
          ...p,
          suggested_oils: updatedOils,
          isEnriched: allOilsProcessed
        };
      }
      return p;
    });

    return {
      therapeuticProperties: updatedProperties,
      lastUpdated: new Date()
    };
  });
},
```

## Final Recipes State Management

### 1. Final Recipes State Structure

```typescript
interface FinalRecipesState {
  morning: {
    recipe: FinalRecipeProtocol | null;
    status: FinalRecipeStatus;
  };
  midDay: {
    recipe: FinalRecipeProtocol | null;
    status: FinalRecipeStatus;
  };
  night: {
    recipe: FinalRecipeProtocol | null;
    status: FinalRecipeStatus;
  };
  isGenerating: boolean;
  hasStartedGeneration: boolean; // Critical flag to prevent infinite loops
  globalError: string | null;
}
```

### 2. Generation State Management

**Critical Pattern** - Preventing infinite loops:
```typescript
setFinalRecipesGenerating: (isGenerating: boolean) => {
  set((state) => ({
    finalRecipes: {
      ...state.finalRecipes,
      isGenerating,
      hasStartedGeneration: isGenerating || state.finalRecipes.hasStartedGeneration
    },
    lastUpdated: new Date()
  }));
},
```

**Individual Recipe Updates**:
```typescript
updateFinalRecipes: (timeSlot: RecipeTimeSlot, recipe: FinalRecipeProtocol) => {
  set((state) => ({
    finalRecipes: {
      ...state.finalRecipes,
      [timeSlot === 'mid-day' ? 'midDay' : timeSlot]: {
        recipe,
        status: { status: 'success' as const, retry_count: 0 }
      }
    },
    lastUpdated: new Date()
  }));
},
```

## Performance Optimization Patterns

### 1. Conditional State Updates

**Only Update When Changed**:
```typescript
setLoading: (loading: boolean) => {
  set((state) => {
    // Only update if loading state actually changed
    if (state.isLoading === loading) return state;
    return {
      isLoading: loading,
      lastUpdated: new Date()
    };
  });
},
```

### 2. Batched Operations

**Streaming State Batching**:
```typescript
// Use individual setters instead of non-existent batch method
if (updates.isStreamingCauses !== undefined) {
  store.setStreamingCauses(updates.isStreamingCauses);
}
if (updates.isStreamingSymptoms !== undefined) {
  store.setStreamingSymptoms(updates.isStreamingSymptoms);
}
// ... additional updates
```

## Critical State Management Insights

### 1. Data Source Priority

**Primary Data Source**: `therapeuticProperties` (NOT `suggestedOils`)
- Final recipes component correctly uses `therapeuticProperties` as primary source
- `suggestedOils` array is legacy and often empty
- Oil data is nested within therapeutic properties

### 2. State Synchronization

**Auto-Generation Trigger Pattern**:
```typescript
useEffect(() => {
  // NEW VALIDATION: Use therapeuticProperties with enriched oils
  const hasTherapeuticPropertiesWithOils = therapeuticProperties.some(prop =>
    prop.isEnriched && prop.suggested_oils && prop.suggested_oils.length > 0
  );

  const hasRequiredData = healthConcern && demographics &&
    selectedCauses.length > 0 && selectedSymptoms.length > 0 &&
    hasTherapeuticPropertiesWithOils;

  if (hasRequiredData && !finalRecipes.hasStartedGeneration) {
    handleGenerateRecipes();
  }
}, [
  // Use stable primitive values instead of object references
  !!healthConcern,
  !!demographics,
  selectedCauses.length,
  selectedSymptoms.length,
  therapeuticProperties.length,
  finalRecipes.hasStartedGeneration,
  handleGenerateRecipes
]);
```

### 3. Error Recovery Patterns

**State Reset on Error**:
```typescript
catch (error) {
  // CRITICAL: Reset generation flag on error to allow retry
  setFinalRecipesGenerating(false);
  // CRITICAL: Reset execution guard on error to allow retry
  autoTriggerExecutedRef.current = false;
  completeAIStreaming('final-recipes', new Map());
}
```

## Technical Summary

The state management implementation follows these key patterns:

1. **Non-Persistent Architecture**: Intentional design for fresh sessions
2. **Optimized Updates**: Conditional updates to minimize re-renders
3. **Dependency-Based Clearing**: Cascading data cleanup on navigation
4. **Granular Selectors**: Component-specific state subscriptions
5. **Batched Operations**: Grouped updates for performance
6. **Error Recovery**: Robust error handling with state reset capabilities

**Key Files**:
- **`recipe-store.ts`**: Core state management with Zustand
- **`use-recipe-navigation.ts`**: Navigation state management
- **`use-batched-recipe-updates.ts`**: Optimized update patterns
- **`final-recipes-display.tsx`**: Component-level state consumption

**Critical State Dependencies**:
- **`therapeuticProperties`**: Primary data source for final recipes
- **`hasStartedGeneration`**: Critical flag preventing infinite loops
- **`isEnriched`**: Property-level completion tracking
- **`sessionId`**: Session tracking for debugging and analytics