# Wrapper Components Audit

## Summary
Found **2 wrapper components** that need to be migrated to next-intl patterns.

## Wrapper Components Analysis

### 1. `src/features/homepage/layout/homepage-layout-i18n.tsx`
- **Wraps**: `src/features/homepage/layout/homepage-layout.tsx` (HomepageLayout)
- **Type**: Client component wrapper with custom i18n context
- **Namespace**: `homepage`
- **Migration Strategy**: Can be converted to server component since it's mainly layout
- **Complexity**: Medium - Creates custom i18n context and provider
- **Key Features**:
  - Creates I18nContext for child components
  - Provides `useServerI18n` hook
  - Handles namespace:key translation format
  - Maintains all existing layout structure

### 2. `src/features/homepage/components/hero-content/hero-content-i18n.tsx`
- **Wraps**: `src/features/homepage/components/hero-content/hero-content.tsx` (HeroContent)
- **Type**: Client component using custom i18n context
- **Namespace**: `homepage`
- **Migration Strategy**: Keep as client component (uses framer-motion animations)
- **Complexity**: Low - Just consumes context from parent wrapper
- **Key Features**:
  - Uses `useServerI18n()` from parent context
  - Heavy use of animations (framer-motion)
  - Form interactions require client-side behavior

## Original Components Analysis

### `homepage-layout.tsx` (Original)
- **Current State**: Uses basic structure without i18n
- **Can be Server Component**: Yes - mainly layout and structure
- **Dependencies**: LoadingProvider, HeroHeader, HeroCanvasBackground, HeroContent

### `hero-content.tsx` (Original) 
- **Current State**: Already uses `useTranslations('homepage')` from next-intl!
- **Must be Client Component**: Yes - uses framer-motion, form interactions
- **Dependencies**: framer-motion, next-intl (already configured!)

## Key Findings

1. **Good News**: The original `hero-content.tsx` already uses next-intl's `useTranslations('homepage')`!
2. **Wrapper Pattern**: The `-i18n.tsx` files create a custom context system that duplicates next-intl functionality
3. **Migration Path**: We can eliminate the wrappers and use the original components directly
4. **Server vs Client**: Layout can be server component, content must remain client component

## Migration Priority

### High Priority (Simple)
1. **hero-content.tsx** - Already uses next-intl, just need to update imports
2. **homepage-layout.tsx** - Can be converted to server component

### Medium Priority (Wrapper Elimination)
1. Delete `hero-content-i18n.tsx` wrapper
2. Delete `homepage-layout-i18n.tsx` wrapper
3. Update imports in parent components

## Translation Namespaces Used
- `homepage` - All hero content translations
- Keys used: `hero.announcement`, `hero.title`, `hero.rotatingWords.*`, `hero.subtitle`, etc.

## Next Steps
1. Update original components to use next-intl properly
2. Convert layout to server component where possible
3. Update parent component imports
4. Delete wrapper files
5. Test functionality in all locales