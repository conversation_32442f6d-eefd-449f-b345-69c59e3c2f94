<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Plano Terapêutico - Aromaterapia</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <style>
        /* Estilos Globais e da Página Macro */
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8fafc;
        }

        .main-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }

        /* Estilos das Abas */
        /* Subtle highlight for the currently selected protocol in the timeline */
        .timeline-item.active-box > div[class^='bg-'] {
            box-shadow: 0 2px 8px 0 rgba(56, 178, 172, 0.07), 0 0 0 2px #38b2ac22;
            background-color: #f0fdfa !important;
            border: 1.5px solid #5eead4;
            transition: box-shadow 0.18s, border 0.18s, background 0.18s;
        }
        /* Only apply .active-box to the currently selected protocol item in the timeline */
        /* Remove .active-box from other timeline-item divs when selection changes. */
        .tabs-nav {
            display: flex;
            gap: 1.5rem;
            border-bottom: 2px solid #e2e8f0;
            margin-bottom: 2rem;
        }

        .tab-button {
            display: flex;
            align-items: center;
            gap: 0.5rem;
            padding: 0.75rem 0.25rem;
            border: none;
            background: none;
            cursor: pointer;
            font-size: 1rem;
            font-weight: 500;
            color: #64748b;
            border-bottom: 2px solid transparent;
            transform: translateY(2px);
            transition: all 0.2s ease-in-out;
        }

        .tab-button:hover {
            color: #319795;
        }

        .tab-button.active {
            color: #319795;
            font-weight: 600;
            border-bottom-color: #319795;
        }

        .tab-content {
            display: none;
            max-width: 1200px;
            margin: 0 auto;
            width: 100%;
            padding: 0 2rem;
            box-sizing: border-box;
        }

        .tab-content.active {
            display: block;
        }

        /* Estilos dos Cards da Visão Geral */
        .info-card {
            background: white;
            border-radius: 1rem;
            padding: 1.5rem;
            box-shadow: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
        }

        .tag {
            display: inline-block;
            padding: 0.25rem 0.75rem;
            border-radius: 9999px;
            font-size: 0.8rem;
            font-weight: 500;
        }
        
        /* Estilos do Cartão de Receita (Copiado do artefato anterior) */
        .protocol-card {
            background: white;
            border-radius: 20px;
            box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
            overflow: hidden;
            max-width: 550px; /* Increased width for new layout */
            width: 100%;
            position: relative;
            transition: all 0.3s ease;
        }

        .protocol-header {
            background: linear-gradient(135deg, #38b2ac, #319795);
            color: white;
            padding: 24px 24px 60px;
            position: relative;
        }
        
        .time-badge {
            position: absolute;
            top: 16px;
            right: 16px;
            background: rgba(255, 255, 255, 0.2);
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 0.8rem;
            font-weight: 600;
            z-index: 10;
        }

        .protocol-title { font-size: 1.8rem; font-weight: 700; margin-bottom: 8px; }
        .protocol-subtitle { font-size: 1rem; opacity: 0.9; }

        .recipe-visual {
            background: white;
            margin: -40px 24px 20px;
            border-radius: 16px;
            padding: 20px;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.08);
            position: relative;
            z-index: 5;
        }
        
        .droplet-visualizer { display: flex; justify-content: center; align-items: flex-end; gap: 4px; height: 40px; padding-bottom: 16px; margin-bottom: 8px; }
        .droplet { width: 8px; border-radius: 50%; animation: float 2s ease-in-out infinite; }
        .droplet.lavanda { background-color: #a78bfa; }
        .droplet.olibano { background-color: #fb923c; }
        .droplet.copaiba { background-color: #4ade80; }
        .droplet.bergamota { background-color: #f59e0b; }
        .droplet.alecrim { background-color: #16a34a; }
        .droplet.hortela { background-color: #2dd4bf; }
        @keyframes float { 0%, 100% { transform: translateY(0); } 50% { transform: translateY(-8px); } }

        .ingredient-item { display: flex; align-items: center; gap: 12px; padding: 8px 0; }
        .ingredient-badge { flex-shrink: 0; text-align: center; font-size: 0.8rem; font-weight: 600; background-color: #e6fffa; color: #2c7a7b; border-radius: 8px; width: 60px; padding: 8px 4px; }
        .ingredient-details .name { font-weight: 600; color: #2d3748; }
        .ingredient-details .botanical { font-size: 0.8rem; font-style: italic; color: #718096; }

        .collapsible-strip { list-style: none; cursor: pointer; background: linear-gradient(135deg, #4fd1c5, #38b2ac); color: white; padding: 16px 24px; transition: background 0.3s ease; }
        .collapsible-strip:hover { background: linear-gradient(135deg, #38b2ac, #4fd1c5); }
        .collapsible-strip .title { font-weight: 600; }
        .collapsible-strip .subtitle { font-size: 0.8rem; opacity: 0.8; }
        .collapsible-content { background-color: #f7fafc; padding: 20px 24px; }
        .collapsible-strip::-webkit-details-marker { display: none; }
        .collapsible-strip .arrow { transition: transform 0.3s ease; }
        details[open] .collapsible-strip .arrow { transform: rotate(90deg); }

        /* Flip card styles for Resumo dos Protocolos */
        .flip-card {
          perspective: 1000px;
        }
        .flip-card-inner {
          position: relative;
          width: 100%;
          height: 100%;
          transition: transform 0.8s;
          transform-style: preserve-3d;
        }
        .flip-card.is-flipped .flip-card-inner {
          transform: rotateY(180deg);
        }
        .flip-card-front, .flip-card-back {
          position: absolute;
          width: 100%;
          height: 100%;
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
          border-radius: 1rem;
          overflow: hidden;
        }
        .flip-card-back {
          transform: rotateY(180deg);
        }
        /* --- Timeline styles from ideas-to-implement-v2.html for pixel-perfect match --- */
        .timeline {
            position: relative;
        }
        .timeline::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 1.5rem;
            bottom: 1.5rem;
            width: 3px;
            background-color: #cbd5e1;
        }
        .timeline-item .dot {
            position: absolute;
            left: 1rem;
            top: 1.5rem;
            transform: translateX(-50%);
            width: 1.25rem;
            height: 1.25rem;
            border-radius: 9999px;
            background-color: white;
            border: 3px solid #cbd5e1;
            transition: all 0.3s ease;
        }
        .timeline-item.active .dot {
            border-color: #38b2ac;
            background-color: #38b2ac;
        }
    </style>
</head>
<body>

    <div class="main-container">
        <!-- Navegação por Abas -->
        <nav class="tabs-nav sticky top-0 z-10 bg-white/80 backdrop-blur-sm">
            <button class="tab-button active" onclick="switchTab(event, 'overview')">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                Visão Geral
            </button>
            <button class="tab-button" onclick="switchTab(event, 'recipes')">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" /></svg>
                Receitas
            </button>
            <button class="tab-button" onclick="switchTab(event, 'studies')">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 20h9" /><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16.5 3.5a2.121 2.121 0 013 3L7 19.5 3 21l1.5-4L16.5 3.5z" /></svg>
                Estudos Científicos
            </button>
            <button class="tab-button" onclick="switchTab(event, 'security')">
                <svg class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg>
                Segurança
            </button>
        </nav>

        <!-- Conteúdo das Abas -->
        <main>
            <!-- Aba: Visão Geral -->
            <div id="overview" class="tab-content active">
                <div class="space-y-8">
                    <div class="grid grid-cols-1 lg:grid-cols-5 gap-8">
                        <!-- Coluna Esquerda: Perfil -->
                        <div class="lg:col-span-2">
                            <div class="info-card h-full">
                                <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-teal-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M5.121 17.804A13.937 13.937 0 0112 16c2.5 0 4.847.655 6.879 1.804M15 10a3 3 0 11-6 0 3 3 0 016 0m-6 4a9 9 0 11-18 0 9 9 0 0118 0z" /></svg>
                                    Perfil do Usuário
                                </h2>
                                <div class="space-y-3 text-gray-600">
                                    <div class="flex justify-between"><strong>Condição:</strong> <span>Dores de cabeça crônicas</span></div>
                                    <div class="flex justify-between"><strong>Idade:</strong> <span>3 anos</span></div>
                                    <div class="flex justify-between"><strong>Gênero:</strong> <span>Feminino</span></div>
                                    <div class="flex justify-between"><strong>Gravidade:</strong> <span>Moderada</span></div>
                                    <div class="pt-2">
                                        <h3 class="font-semibold text-gray-700 mb-2">Causas Identificadas:</h3>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="tag bg-red-100 text-red-800">Estresse</span>
                                            <span class="tag bg-red-100 text-red-800">Privação de sono</span>
                                        </div>
                                    </div>
                                    <div class="pt-2">
                                        <h3 class="font-semibold text-gray-700 mb-2">Sintomas:</h3>
                                        <div class="flex flex-wrap gap-2">
                                            <span class="tag bg-orange-100 text-orange-800">Dor constante</span>
                                            <span class="tag bg-orange-100 text-orange-800">Tensão muscular</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <!-- Coluna Direita: Estratégia -->
                        <div class="lg:col-span-3">
                            <div class="info-card h-full">
                                 <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-teal-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2m0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" /></svg>
                                    Estratégia Terapêutica
                                </h2>
                                <div class="space-y-4">
                                    <div>
                                        <h3 class="font-semibold text-gray-700 mb-3">Propriedades Terapêuticas:</h3>
                                        <div class="grid grid-cols-1 sm:grid-cols-2 gap-3">
                                            <div class="bg-teal-50 p-3 rounded-lg"><strong class="text-teal-800">Calmante</strong><p class="text-sm text-teal-700">Reduz ansiedade</p></div>
                                            <div class="bg-teal-50 p-3 rounded-lg"><strong class="text-teal-800">Relaxante</strong><p class="text-sm text-teal-700">Alivia tensão</p></div>
                                            <div class="bg-teal-50 p-3 rounded-lg"><strong class="text-teal-800">Anti-stress</strong><p class="text-sm text-teal-700">Gestão emocional</p></div>
                                            <div class="bg-teal-50 p-3 rounded-lg"><strong class="text-teal-800">Indutor do sono</strong><p class="text-sm text-teal-700">Melhora descanso</p></div>
                                        </div>
                                    </div>
                                    <div>
                                        <h3 class="font-semibold text-gray-700 mb-2">Métodos de Aplicação:</h3>
                                        <div class="flex flex-wrap gap-3 text-gray-600">
                                            <span>Tópica (roll-on)</span>
                                            <span>Aromática (difusão)</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Resumo dos Protocolos -->
                    <div class="lg:col-span-5">
                         <div class="info-card">
                             <h2 class="text-xl font-bold text-gray-800 mb-4 flex items-center gap-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-teal-600" fill="none" viewBox="0 0 24 24" stroke="currentColor" stroke-width="2"><path stroke-linecap="round" stroke-linejoin="round" d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01" /></svg>
                                Resumo dos Protocolos
                            </h2>
                            <div class="w-full mx-auto grid grid-cols-1 md:grid-cols-3 gap-8">
                                <!-- Matinal Card -->
                                <div class="h-[380px] flip-card flex flex-col" id="flipResumoMatinal">
                                    <div class="flip-card-inner flex-1">
                                        <!-- Frente -->
                                        <div class="flip-card-front bg-gradient-to-br from-slate-800 to-slate-900 p-8 flex flex-col items-center justify-between text-white shadow-2xl shadow-slate-500/30 rounded-2xl h-full">
                                            <div class="w-full flex flex-col items-start">
                                                <span class="text-sm uppercase tracking-widest text-teal-300">PROTOCOLO</span>
                                                <div class="flex items-center gap-2 mt-2">
                                                    <span class="text-2xl">🌅</span>
                                                    <h3 class="text-2xl font-black">Matinal</h3>
                                                </div>
                                            </div>
                                            <div class="flex-1 flex flex-col items-center justify-center w-full mt-4">
                                                <div class="text-center">
                                                    <div class="text-base text-slate-300">Sinergia para</div>
                                                    <div class="text-xl font-bold mb-6">Foco & Calma</div>
                                                </div>
                                                <button onclick="flipCard(this)" class="inline-flex items-center gap-2 px-6 py-2.5 bg-white text-slate-900 font-semibold rounded-full shadow-lg hover:bg-slate-200 transition-all duration-200">
                                                    Ver Detalhes
                                                    <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'><path fill-rule='evenodd' d='M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z' clip-rule='evenodd'/></svg>
                                                </button>
                                            </div>
                                            <div class="text-center text-xs text-slate-400 mt-4 w-full">
                                                Clique no botão para ver os detalhes
                                            </div>
                                        </div>
                                        <!-- Verso -->
                                        <div class="flip-card-back bg-white p-6 overflow-y-auto relative rounded-2xl border border-gray-200 shadow-lg">
                                            <button
                                                onclick="flipCard(this)"
                                                class="absolute top-4 right-4 text-slate-400 hover:text-teal-700 text-2xl leading-none rounded-full focus:outline-none focus:ring-2 focus:ring-teal-400"
                                                aria-label="Fechar"
                                                type="button"
                                            >&times;</button>
                                            <div class="mt-2">
                                                <div class="mb-2 text-sm text-gray-700"><strong>Objetivo:</strong> Gestão de estresse e prevenção</div>
                                            </div>
                                            <div class="mt-4">
                                                <ul class="mt-2 space-y-1">
                                                    <li class="flex justify-between items-center"><span class="text-violet-600">● Lavanda</span> <span class="font-mono text-sm">5 gotas</span></li>
                                                    <li class="flex justify-between items-center"><span class="text-orange-600">● Olíbano</span> <span class="font-mono text-sm">3 gotas</span></li>
                                                    <li class="flex justify-between items-center"><span class="text-lime-600">● Copaíba</span> <span class="font-mono text-sm">2 gotas</span></li>
                                                </ul>
                                            </div>
                                            <div class="mt-4">
                                                <div class="mb-2 text-sm text-gray-700"><strong>Preparo rápido:</strong> Misture os óleos em um frasco roll-on de 10ml e complete com óleo de Jojoba. Agite bem antes de usar.</div>
                                                <div class="mb-2 text-sm text-gray-700"><strong>Como Usar:</strong> Aplique nos pulsos, nuca e têmporas ao acordar para um início de dia equilibrado.</div>
                                            </div>
                                            <div class="mt-2 text-right">
                                                <a href="#" onclick="return switchToRecipe('matinal');" class="text-sm text-teal-600 hover:text-teal-800 hover:underline">Ver receita →</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Diurno Card -->
                                <div class="h-[380px] flip-card flex flex-col" id="flipResumoDiurno">
                                    <div class="flip-card-inner flex-1">
                                        <!-- Frente -->
                                        <div class="flip-card-front bg-gradient-to-br from-yellow-400 to-orange-500 p-8 flex flex-col items-center justify-between text-white shadow-2xl shadow-orange-300/30 rounded-2xl h-full">
                                            <div class="w-full flex flex-col items-start">
                                                <span class="text-sm uppercase tracking-widest text-yellow-100">PROTOCOLO</span>
                                                <div class="flex items-center gap-2 mt-2">
                                                    <span class="text-2xl">☀️</span>
                                                    <h3 class="text-2xl font-black">Diurno</h3>
                                                </div>
                                            </div>
                                            <div class="flex-1 flex flex-col items-center justify-center w-full mt-4">
                                                <div class="text-center">
                                                    <div class="text-base text-yellow-100">Alívio imediato da dor</div>
                                                    <div class="text-xl font-bold mb-6">&nbsp;</div>
                                                </div>
                                                <button onclick="flipCard(this)" class="inline-flex items-center gap-2 px-6 py-2.5 bg-white text-orange-700 font-semibold rounded-full shadow-lg hover:bg-orange-100 transition-all duration-200">
                                                    Ver Detalhes
                                                    <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'><path fill-rule='evenodd' d='M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z' clip-rule='evenodd'/></svg>
                                                </button>
                                            </div>
                                            <div class="text-center text-xs text-yellow-50 mt-4 w-full">
                                                Clique no botão para ver os detalhes
                                            </div>
                                        </div>
                                        <!-- Verso -->
                                        <div class="flip-card-back bg-white p-6 overflow-y-auto relative rounded-2xl border border-gray-200 shadow-lg">
                                            <button
                                                onclick="flipCard(this)"
                                                class="absolute top-4 right-4 text-slate-400 hover:text-orange-700 text-2xl leading-none rounded-full focus:outline-none focus:ring-2 focus:ring-orange-400"
                                                aria-label="Fechar"
                                                type="button"
                                            >&times;</button>
                                            <div class="mt-2">
                                                <div class="mb-2 text-sm text-gray-700"><strong>Objetivo:</strong> Alívio imediato da dor</div>
                                            </div>
                                            <div class="mt-4">
                                                <ul class="mt-2 space-y-1">
                                                    <li class="flex justify-between items-center"><span class="text-violet-600">● Lavanda</span> <span class="font-mono text-sm">4 gotas</span></li>
                                                    <li class="flex justify-between items-center"><span class="text-green-600">● Hortelã</span> <span class="font-mono text-sm">3 gotas</span></li>
                                                    <li class="flex justify-between items-center"><span class="text-lime-600">● Copaíba</span> <span class="font-mono text-sm">3 gotas</span></li>
                                                </ul>
                                            </div>
                                            <div class="mt-4">
                                                <div class="mb-2 text-sm text-gray-700"><strong>Preparo rápido:</strong> Misture os óleos em um frasco roll-on de 10ml e complete com óleo de Jojoba. Agite bem antes de usar.</div>
                                                <div class="mb-2 text-sm text-gray-700"><strong>Como Usar:</strong> Aplique nas têmporas e na nuca quando sentir necessidade de alívio e clareza mental.</div>
                                            </div>
                                            <div class="mt-2 text-right">
                                                <a href="#" onclick="return switchToRecipe('diurno');" class="text-sm text-orange-600 hover:text-orange-800 hover:underline">Ver receita →</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <!-- Noturno Card -->
                                <div class="h-[380px] flip-card flex flex-col" id="flipResumoNoturno">
                                    <div class="flip-card-inner flex-1">
                                        <!-- Frente -->
                                        <div class="flip-card-front bg-gradient-to-br from-indigo-500 to-indigo-900 p-8 flex flex-col items-center justify-between text-white shadow-2xl shadow-indigo-500/30 rounded-2xl h-full">
                                            <div class="w-full flex flex-col items-start">
                                                <span class="text-sm uppercase tracking-widest text-indigo-100">PROTOCOLO</span>
                                                <div class="flex items-center gap-2 mt-2">
                                                    <span class="text-2xl">🌙</span>
                                                    <h3 class="text-2xl font-black">Noturno</h3>
                                                </div>
                                            </div>
                                            <div class="flex-1 flex flex-col items-center justify-center w-full mt-4">
                                                <div class="text-center">
                                                    <div class="text-base text-indigo-100">Sono reparador e relaxamento</div>
                                                    <div class="text-xl font-bold mb-6">&nbsp;</div>
                                                </div>
                                                <button onclick="flipCard(this)" class="inline-flex items-center gap-2 px-6 py-2.5 bg-white text-indigo-700 font-semibold rounded-full shadow-lg hover:bg-indigo-100 transition-all duration-200">
                                                    Ver Detalhes
                                                    <svg xmlns='http://www.w3.org/2000/svg' class='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'><path fill-rule='evenodd' d='M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z' clip-rule='evenodd'/></svg>
                                                </button>
                                            </div>
                                            <div class="text-center text-xs text-indigo-50 mt-4 w-full">
                                                Clique no botão para ver os detalhes
                                            </div>
                                        </div>
                                        <!-- Verso -->
                                        <div class="flip-card-back bg-white p-6 overflow-y-auto relative rounded-2xl border border-gray-200 shadow-lg">
                                            <button
                                                onclick="flipCard(this)"
                                                class="absolute top-4 right-4 text-slate-400 hover:text-indigo-700 text-2xl leading-none rounded-full focus:outline-none focus:ring-2 focus:ring-indigo-400"
                                                aria-label="Fechar"
                                                type="button"
                                            >&times;</button>
                                            <div class="mt-2">
                                                <div class="mb-2 text-sm text-gray-700"><strong>Objetivo:</strong> Sono reparador e relaxamento</div>
                                            </div>
                                            <div class="mt-4">
                                                <ul class="mt-2 space-y-1">
                                                    <li class="flex justify-between items-center"><span class="text-violet-600">● Lavanda</span> <span class="font-mono text-sm">6 gotas</span></li>
                                                    <li class="flex justify-between items-center"><span class="text-yellow-600">● Camomila Romana</span> <span class="font-mono text-sm">3 gotas</span></li>
                                                    <li class="flex justify-between items-center"><span class="text-lime-600">● Erva-cidreira</span> <span class="font-mono text-sm">1 gota</span></li>
                                                </ul>                                             
                                            </div>
                                            <div class="mt-4">
                                                <div class="mb-2 text-sm text-gray-700"><strong>Preparo rápido:</strong> Misture os óleos em um frasco roll-on de 10ml e complete com óleo de Jojoba. Agite bem antes de usar.</div>
                                                <div class="mb-2 text-sm text-gray-700"><strong>Como Usar:</strong> Aplique na sola dos pés e no peito 30 minutos antes de dormir para uma noite de sono tranquila.</div>
                                            </div>
                                            <div class="mt-2 text-right">
                                                <a href="#" onclick="return switchToRecipe('noturno');" class="text-sm text-indigo-600 hover:text-indigo-800 hover:underline">Ver receita →</a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                         </div>
                    </div>
                </div>
            </div>

            <!-- Aba: Receitas -->
            <div id="recipes" class="tab-content">
                <div class="w-full grid grid-cols-1 md:grid-cols-4 gap-8 px-2 sm:px-4 md:px-0">
                    <!-- Timeline Navigation -->
                    <aside class="md:col-span-1 flex flex-col items-center md:items-stretch">
                        <div class="relative timeline flex flex-col h-full">
                             <!-- Item 1 -->
                            <div class="timeline-item relative pl-10 pb-8 active cursor-pointer" data-protocol="matinal" onclick="switchProtocol('matinal')">
                                <div class="dot"></div>
                                <div class="bg-teal-100 p-4 rounded-lg">
                                    <p class="text-sm text-teal-800">07:00 - 09:00</p>
                                    <h4 class="font-bold text-teal-900">Protocolo Matinal</h4>
                                    <p class="text-sm text-teal-700">Foco e calma para começar o dia</p>
                                </div>
                            </div>
                            <!-- Item 2 -->
                            <div class="timeline-item relative pl-10 pb-8 cursor-pointer" data-protocol="diurno" onclick="switchProtocol('diurno')">
                                <div class="dot"></div>
                                <div class="bg-orange-50 p-4 rounded-lg">
                                    <p class="text-sm text-orange-800">10:00 - 17:00</p>
                                    <h4 class="font-bold text-orange-900">Protocolo Diurno</h4>
                                    <p class="text-sm text-orange-700">Energia e concentração</p>
                                </div>
                            </div>
                            <!-- Item 3 -->
                            <div class="timeline-item relative pl-10 cursor-pointer" data-protocol="noturno" onclick="switchProtocol('noturno')">
                                <div class="dot"></div>
                                <div class="bg-indigo-50 p-4 rounded-lg">
                                    <p class="text-sm text-indigo-800">22:00 - 23:00</p>
                                    <h4 class="font-bold text-indigo-900">Protocolo Noturno</h4>
                                    <p class="text-sm text-indigo-700">Relaxamento e sono reparador</p>
                                </div>
                            </div>
                        </div>
                    </aside>
                    
                    <!-- Protocol Cards Container -->
                    <section class="md:col-span-3 flex flex-col items-center gap-8 w-full">
                        <!-- Protocolo Matinal (default visible) -->
                        <div id="protocol-matinal" class="protocol-card w-full max-w-lg sm:max-w-xl md:max-w-2xl lg:max-w-3xl flex flex-col">
                            <div class="time-badge flex flex-col items-center justify-center gap-1">
                                <span class="font-bold">7h - 9h</span>
                                <span class="inline-block bg-teal-100 text-teal-800 text-xs font-semibold px-3 py-1 rounded-full">Uso Tópico</span>
                            </div>
                            <div class="protocol-header">
                                <div class="protocol-title">🌅 Protocolo Matinal</div>
                                <div class="protocol-subtitle">Sinergia para Foco e Calma</div>
                            </div>
                            <div class="recipe-visual">
                                <div class="droplet-visualizer">
                                    <div class="droplet lavanda h-6" style="animation-delay: 0.1s;"></div><div class="droplet lavanda h-6" style="animation-delay: 0.2s;"></div><div class="droplet lavanda h-6" style="animation-delay: 0.3s;"></div><div class="droplet lavanda h-6" style="animation-delay: 0.4s;"></div><div class="droplet lavanda h-6" style="animation-delay: 0.5s;"></div>
                                    <div class="droplet olibano h-5 ml-2" style="animation-delay: 0.6s;"></div><div class="droplet olibano h-5" style="animation-delay: 0.7s;"></div><div class="droplet olibano h-5" style="animation-delay: 0.8s;"></div>
                                    <div class="droplet copaiba h-4 ml-2" style="animation-delay: 0.9s;"></div><div class="droplet copaiba h-4" style="animation-delay: 1.0s;"></div>
                                </div>
                                <div class="quick-info" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 1rem; padding: 1rem 0 0.5rem 0; background: none; border-bottom: none;">
                                    <div class="info-item">
                                        <div class="label text-xs text-gray-500 font-normal uppercase tracking-wide">Total Gotas</div>
                                        <div class="value text-lg font-bold text-gray-800">10</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label text-xs text-gray-500 font-normal uppercase tracking-wide">Diluição</div>
                                        <div class="value text-lg font-bold text-gray-800">1%</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label text-xs text-gray-500 font-normal uppercase tracking-wide">Tamanho</div>
                                        <div class="value text-lg font-bold text-gray-800">10ml</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label text-xs text-gray-500 font-normal uppercase tracking-wide">Tampa</div>
                                        <div class="value text-lg font-bold text-gray-800">Roll-on</div>
                                    </div>
                                </div>
                                <div class="text-center text-lg text-gray-700 font-bold pb-4 border-b border-gray-200 mb-4">
                                    <!-- Ingredientes e Formulação removed -->
                                </div>
                                
                                <!-- Responsive Grid for Ingredients -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Essential Oils -->
                                    <div>
                                        <h5 class="text-sm font-semibold text-gray-700 uppercase tracking-wide mb-2">Óleos Essenciais</h5>
                                        <div class="space-y-2 border-t pt-2">
                                            <div class="ingredient-item"><div class="ingredient-badge">5 gotas</div><div class="ingredient-details"><div class="name">Lavanda</div><div class="botanical">Lavandula angustifolia</div></div></div>
                                            <div class="ingredient-item"><div class="ingredient-badge">3 gotas</div><div class="ingredient-details"><div class="name">Olíbano</div><div class="botanical">Boswellia carterii</div></div></div>
                                            <div class="ingredient-item"><div class="ingredient-badge">2 gotas</div><div class="ingredient-details"><div class="name">Copaíba</div><div class="botanical">Copaifera officinalis</div></div></div>
                                        </div>
                                    </div>
                                    <!-- Carrier Oil -->
                                    <div>
                                         <h5 class="text-sm font-semibold text-gray-700 uppercase tracking-wide mb-2">Óleo Carreador</h5>
                                         <div class="space-y-2 border-t pt-2">
                                             <div class="bg-teal-50 p-3 rounded-lg border border-teal-100">
                                                 <p class="font-bold text-teal-900">Jojoba <span class="font-medium text-teal-800">(Recomendado)</span></p>
                                                 <p class="text-xs text-teal-700">Propriedades: Anti-inflamatório, hidratante</p>
                                             </div>
                                             <div class="mt-4">
                                                 <div class="bg-blue-50 p-3 rounded-lg border border-blue-100">
                                                     <p class="font-bold text-blue-900">Semente de Uva <span class="font-medium text-blue-800">(Alternativa)</span></p>
                                                     <p class="text-xs text-blue-700">Propriedades: Leve, rápida absorção, ideal para peles sensíveis</p>
                                                 </div>
                                             </div>
                                         </div>
                                    </div>
                                </div>
                            </div>
                            <div class="w-full">
                                <details><summary class="collapsible-strip flex justify-between items-center"><div class="flex items-center gap-3"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg><div><div class="title">Como Usar</div><div class="subtitle">Modos de aplicação e frequência</div></div></div><svg class="arrow w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg></summary><div class="collapsible-content">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <!-- Item 1 -->
        <div class="bg-slate-50 p-4 rounded-lg text-left">
            <h5 class="font-semibold text-gray-800">1. Aplicação Tópica</h5>
            <p class="text-sm text-gray-600">Aplicar nos pulsos, nuca e atrás das orelhas.</p>
            <span class="inline-block mt-2 text-xs font-medium bg-teal-100 text-teal-800 px-2 py-0.5 rounded-full">⏰ 1x pela manhã</span>
        </div>
        <!-- Item 2 -->
        <div class="bg-slate-50 p-4 rounded-lg text-left">
            <h5 class="font-semibold text-gray-800">2. Difusão Aromática</h5>
            <p class="text-sm text-gray-600">Adicionar 3-4 gotas no difusor por 30 minutos.</p>
            <span class="inline-block mt-2 text-xs font-medium bg-cyan-100 text-cyan-800 px-2 py-0.5 rounded-full">☕ Durante o café da manhã</span>
        </div>
        <!-- Item 3 (placeholder) -->
        <div class="bg-slate-50 p-4 rounded-lg text-left">
             <h5 class="font-semibold text-gray-800">3. Inalação Direta</h5>
             <p class="text-sm text-gray-600">Inalar diretamente do frasco por 1-2 minutos.</p>
             <span class="inline-block mt-2 text-xs font-medium bg-indigo-100 text-indigo-800 px-2 py-0.5 rounded-full">🆘 Em crises</span>
        </div>
    </div>
</div></details>
                                <details><summary class="collapsible-strip flex justify-between items-center"><div class="flex items-center gap-3"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" /></svg><div><div class="title">Instruções de Preparo</div><div class="subtitle">Passo a passo da mistura</div></div></div><svg class="arrow w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg></summary>
  <div class="collapsible-content">
    <div class="flex flex-col md:flex-row gap-8">
      <!-- Coluna de Ingredientes -->
      <div class="md:w-1/3 bg-slate-50 p-6 rounded-xl">
        <h4 class="font-semibold mb-4 text-slate-800">Ingredientes</h4>
        <ul class="space-y-2 text-sm">
          <li class="flex items-center gap-2"><div class="w-2 h-2 rounded-full bg-violet-500"></div>5 gotas de Lavanda</li>
          <li class="flex items-center gap-2"><div class="w-2 h-2 rounded-full bg-orange-500"></div>3 gotas de Olíbano</li>
          <li class="flex items-center gap-2"><div class="w-2 h-2 rounded-full bg-emerald-500"></div>2 gotas de Copaíba</li>
          <li class="flex items-center gap-2"><div class="w-2 h-2 rounded-full bg-teal-500"></div>~10ml de Óleo de Jojoba</li>
        </ul>
      </div>
      <!-- Coluna do Checklist -->
      <div class="md:w-2/3 bg-slate-50 p-6 rounded-xl">
        <h4 class="font-semibold text-slate-800 mb-4">Checklist do Preparo</h4>
        <div id="checklist-preparo" class="space-y-2">
          <label class="flex items-center gap-2 cursor-pointer text-sm">
            <input type="checkbox" class="h-4 w-4 rounded border-slate-300 text-blue-500 focus:ring-blue-500">
            <span class="flex-1 text-slate-700"><span class="font-bold">Higiene:</span> Lave bem as mãos e o rosto com um sabonete neutro.</span>
          </label>
          <label class="flex items-center gap-2 cursor-pointer text-sm">
            <input type="checkbox" class="h-4 w-4 rounded border-slate-300 text-blue-500 focus:ring-blue-500">
            <span class="flex-1 text-slate-700"><span class="font-bold">Secagem:</span> Seque o rosto suavemente com uma toalha limpa, sem esfregar.</span>
          </label>
          <label class="flex items-center gap-2 cursor-pointer text-sm">
            <input type="checkbox" class="h-4 w-4 rounded border-slate-300 text-blue-500 focus:ring-blue-500">
            <span class="flex-1 text-slate-700"><span class="font-bold">Preparação da Base:</span> Em um pires de vidro ou cerâmica, coloque a quantidade recomendada de óleo carreador.</span>
          </label>
          <label class="flex items-center gap-2 cursor-pointer text-sm">
            <input type="checkbox" class="h-4 w-4 rounded border-slate-300 text-blue-500 focus:ring-blue-500">
            <span class="flex-1 text-slate-700"><span class="font-bold">Adição dos Óleos Essenciais:</span> Adicione o número de gotas de cada óleo essencial, um a um, diretamente no óleo carreador.</span>
          </label>
          <label class="flex items-center gap-2 cursor-pointer text-sm">
            <input type="checkbox" class="h-4 w-4 rounded border-slate-300 text-blue-500 focus:ring-blue-500">
            <span class="flex-1 text-slate-700"><span class="font-bold">Homogeneização:</span> Misture suavemente com a ponta do dedo ou um bastão de vidro limpo até a mistura ficar homogênea.</span>
          </label>
        </div>
        <div class="mt-4 h-2 w-full bg-slate-200 rounded-full overflow-hidden">
          <div id="progressBar-preparo" class="h-full bg-green-500 rounded-full transition-all duration-300" style="width: 0%;"></div>
        </div>
      </div>
    </div>
  </div>
</details>
                                <details><summary class="collapsible-strip flex justify-between items-center"><div class="flex items-center gap-3"><svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" /></svg><div><div class="title">Como Funciona</div><div class="subtitle">A ciência por trás dos óleos</div></div></div><svg class="arrow w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" /></svg></summary>
                                    <div class="collapsible-content space-y-4">
                                        <div class="mb-4">
                                            <h4 class="text-lg font-bold text-cyan-900">Racional</h4>
                                        </div>
                                        <!-- List of oils and explanations starts here -->
                                        <div>
                                            <h5 class="font-semibold text-gray-800">Lavanda</h5>
                                            <p class="text-xs italic text-gray-500 mb-1">Lavandula angustifolia</p>
                                            <div class="flex flex-wrap gap-1 mb-1">
                                                <span class="text-xs font-medium bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full">Calmante</span>
                                                <span class="text-xs font-medium bg-purple-100 text-purple-800 px-2 py-0.5 rounded-full">Relaxante</span>
                                            </div>
                                            <p class="text-sm text-gray-700">Reduz os níveis de cortisol (hormona do stress) e promove o relaxamento do sistema nervoso central.</p>
                                        </div>
                                        <div>
                                            <h5 class="font-semibold text-gray-800">Olíbano</h5>
                                            <p class="text-xs italic text-gray-500 mb-1">Boswellia carterii</p>
                                            <div class="flex flex-wrap gap-1 mb-1">
                                                <span class="text-xs font-medium bg-orange-100 text-orange-800 px-2 py-0.5 rounded-full">Equilíbrio</span>
                                                <span class="text-xs font-medium bg-orange-100 text-orange-800 px-2 py-0.5 rounded-full">Anti-inflamatório</span>
                                            </div>
                                            <p class="text-sm text-gray-700">Ajuda a equilibrar neurotransmissores e possui sesquiterpenos que podem reduzir a inflamação cerebral.</p>
                                        </div>
                                        <div>
                                            <h5 class="font-semibold text-gray-800">Copaíba</h5>
                                            <p class="text-xs italic text-gray-500 mb-1">Copaifera officinalis</p>
                                            <div class="flex flex-wrap gap-1 mb-1">
                                                <span class="text-xs font-medium bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Anti-inflamatório</span>
                                                <span class="text-xs font-medium bg-green-100 text-green-800 px-2 py-0.5 rounded-full">Analgésico</span>
                                            </div>
                                            <p class="text-sm text-gray-700">Rico em beta-cariofileno, atua como um potente anti-inflamatório preventivo no sistema nervoso e alivia a dor.</p>
                                        </div>
                                        <div class="pt-4 mt-4 border-t border-gray-200">
                                            <h5 class="font-semibold text-gray-800">Sinergia e Efeito Combinado</h5>
                                            <p class="text-sm text-gray-700 mt-1">
                                                A sinergia de Lavanda, Olíbano e Copaíba cria uma base protetora para o dia. Juntos, eles atuam gerenciando o estresse e a ansiedade, que são gatilhos para as dores de cabeça. A Lavanda acalma, o Olíbano equilibra as emoções e a Copaíba atua preventivamente. O óleo de Jojoba, como carreador, garante uma absorção suave e contínua, mantendo o equilíbrio ao longo da manhã.
                                            </p>
                                        </div>
                                    </div>
                                </details>
                            </div>
                        </div>
                        
                        <!-- Protocolo Diurno (initially hidden) -->
                        <div id="protocol-diurno" class="protocol-card w-full max-w-lg sm:max-w-xl md:max-w-2xl lg:max-w-3xl flex-col hidden">
                            <div class="time-badge flex flex-col items-center justify-center gap-1">
                                <span class="font-bold">10h - 17h</span>
                                <span class="inline-block bg-orange-100 text-orange-800 text-xs font-semibold px-3 py-1 rounded-full">Uso Tópico</span>
                            </div>
                            <div class="protocol-header">
                                <div class="protocol-title">☀️ Protocolo Diurno</div>
                                <div class="protocol-subtitle">Energia e Concentração Sustentadas</div>
                            </div>
                            <div class="recipe-visual">
                                <div class="droplet-visualizer">
                                    <div class="droplet bergamota h-6" style="animation-delay: 0.1s;"></div>
                                    <div class="droplet bergamota h-6" style="animation-delay: 0.2s;"></div>
                                    <div class="droplet bergamota h-6" style="animation-delay: 0.3s;"></div>
                                    <div class="droplet bergamota h-6" style="animation-delay: 0.4s;"></div>
                                    <div class="droplet alecrim h-5 ml-2" style="animation-delay: 0.5s;"></div>
                                    <div class="droplet alecrim h-5" style="animation-delay: 0.6s;"></div>
                                    <div class="droplet alecrim h-5" style="animation-delay: 0.7s;"></div>
                                    <div class="droplet hortela h-4 ml-2" style="animation-delay: 0.8s;"></div>
                                    <div class="droplet hortela h-4" style="animation-delay: 0.9s;"></div>
                                </div>
                                <div class="quick-info" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); gap: 1rem; padding: 1rem 0 0.5rem 0; background: none; border-bottom: none;">
                                    <div class="info-item">
                                        <div class="label text-xs text-gray-500 font-normal uppercase tracking-wide">Total Gotas</div>
                                        <div class="value text-lg font-bold text-gray-800">9</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label text-xs text-gray-500 font-normal uppercase tracking-wide">Diluição</div>
                                        <div class="value text-lg font-bold text-gray-800">1%</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label text-xs text-gray-500 font-normal uppercase tracking-wide">Tamanho</div>
                                        <div class="value text-lg font-bold text-gray-800">10ml</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="label text-xs text-gray-500 font-normal uppercase tracking-wide">Tampa</div>
                                        <div class="value text-lg font-bold text-gray-800">Roll-on</div>
                                    </div>
                                </div>
                                <div class="text-center text-lg text-gray-700 font-bold pb-4 border-b border-gray-200 mb-4">
                                    <!-- Ingredientes e Formulação -->
                                </div>
                                
                                <!-- Responsive Grid for Ingredients -->
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    <!-- Essential Oils -->
                                    <div>
                                        <h5 class="text-sm font-semibold text-gray-700 uppercase tracking-wide mb-2">Óleos Essenciais</h5>
                                        <div class="space-y-2 border-t pt-2">
                                            <div class="ingredient-item">
                                                <div class="ingredient-badge">4 gotas</div>
                                                <div class="ingredient-details">
                                                    <div class="name">Bergamota</div>
                                                    <div class="botanical">Citrus bergamia</div>
                                                </div>
                                            </div>
                                            <div class="ingredient-item">
                                                <div class="ingredient-badge">3 gotas</div>
                                                <div class="ingredient-details">
                                                    <div class="name">Alecrim</div>
                                                    <div class="botanical">Rosmarinus officinalis</div>
                                                </div>
                                            </div>
                                            <div class="ingredient-item">
                                                <div class="ingredient-badge">2 gotas</div>
                                                <div class="ingredient-details">
                                                    <div class="name">Hortelã-pimenta</div>
                                                    <div class="botanical">Mentha piperita</div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <!-- Carrier Oil -->
                                    <div>
                                        <h5 class="text-sm font-semibold text-gray-700 uppercase tracking-wide mb-2">Óleo Carreador</h5>
                                        <div class="space-y-2 border-t pt-2">
                                            <div class="bg-orange-50 p-3 rounded-lg border border-orange-100">
                                                <p class="font-bold text-orange-900">Amêndoas Doces <span class="font-medium text-orange-800">(Recomendado)</span></p>
                                                <p class="text-xs text-orange-700">Propriedades: Nutritivo, de absorção média</p>
                                            </div>
                                            <div class="mt-4">
                                                <div class="bg-blue-50 p-3 rounded-lg border border-blue-100">
                                                    <p class="font-bold text-blue-900">Semente de Uva <span class="font-medium text-blue-800">(Alternativa)</span></p>
                                                    <p class="text-xs text-blue-700">Propriedades: Leve, rápida absorção</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            <!-- Instruções de Uso -->
                            <div class="w-full">
                                <details>
                                    <summary class="collapsible-strip flex justify-between items-center">
                                        <div class="flex items-center gap-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            <div>
                                                <div class="title">Como Usar</div>
                                                <div class="subtitle">Modos de aplicação e frequência</div>
                                            </div>
                                        </div>
                                        <svg class="arrow w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </summary>
                                    <div class="collapsible-content">
                                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <!-- Item 1 -->
                                            <div class="bg-orange-50 p-4 rounded-lg text-left">
                                                <h5 class="font-semibold text-orange-900">1. Aplicação Tópica</h5>
                                                <p class="text-sm text-orange-800">Aplicar na nuca e pulsos para manter a energia ao longo do dia.</p>
                                                <span class="inline-block mt-2 text-xs font-medium bg-orange-100 text-orange-800 px-2 py-0.5 rounded-full">⏰ A cada 4 horas</span>
                                            </div>
                                            <!-- Item 2 -->
                                            <div class="bg-orange-50 p-4 rounded-lg text-left">
                                                <h5 class="font-semibold text-orange-900">2. Inalação Direta</h5>
                                                <p class="text-sm text-orange-800">Inalar diretamente do frasco para momentos que exigem foco intenso.</p>
                                                <span class="inline-block mt-2 text-xs font-medium bg-orange-100 text-orange-800 px-2 py-0.5 rounded-full">🧠 Quando necessário</span>
                                            </div>
                                            <!-- Item 3 -->
                                            <div class="bg-orange-50 p-4 rounded-lg text-left">
                                                <h5 class="font-semibold text-orange-900">3. Massagem nos Pulsos</h5>
                                                <p class="text-sm text-orange-800">Aplicar uma gota em cada pulso e esfregar suavemente.</p>
                                                <span class="inline-block mt-2 text-xs font-medium bg-orange-100 text-orange-800 px-2 py-0.5 rounded-full">🔄 A cada 2-3 horas</span>
                                            </div>
                                        </div>
                                    </div>
                                </details>

                                <details>
                                    <summary class="collapsible-strip flex justify-between items-center">
                                        <div class="flex items-center gap-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19.428 15.428a2 2 0 00-1.022-.547l-2.387-.477a6 6 0 00-3.86.517l-.318.158a6 6 0 01-3.86.517L6.05 15.21a2 2 0 00-1.806.547M8 4h8l-1 1v5.172a2 2 0 00.586 1.414l5 5c1.26 1.26.367 3.414-1.415 3.414H4.828c-1.782 0-2.674-2.154-1.414-3.414l5-5A2 2 0 009 10.172V5L8 4z" />
                                            </svg>
                                            <div>
                                                <div class="title">Instruções de Preparo</div>
                                                <div class="subtitle">Passo a passo da mistura</div>
                                            </div>
                                        </div>
                                        <svg class="arrow w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </summary>
                                    <div class="collapsible-content">
                                        <div class="flex flex-col md:flex-row gap-8">
                                            <!-- Coluna de Ingredientes -->
                                            <div class="md:w-1/3 bg-orange-50 p-6 rounded-xl">
                                                <h4 class="font-semibold mb-4 text-orange-900">Ingredientes</h4>
                                                <ul class="space-y-2 text-sm">
                                                    <li class="flex items-center gap-2">
                                                        <div class="w-2 h-2 rounded-full bg-amber-400"></div>
                                                        <span>4 gotas de Bergamota</span>
                                                    </li>
                                                    <li class="flex items-center gap-2">
                                                        <div class="w-2 h-2 rounded-full bg-green-600"></div>
                                                        <span>3 gotas de Alecrim</span>
                                                    </li>
                                                    <li class="flex items-center gap-2">
                                                        <div class="w-2 h-2 rounded-full bg-teal-400"></div>
                                                        <span>2 gotas de Hortelã-pimenta</span>
                                                    </li>
                                                    <li class="flex items-center gap-2">
                                                        <div class="w-2 h-2 rounded-full bg-orange-300"></div>
                                                        <span>~10ml de Óleo de Amêndoas</span>
                                                    </li>
                                                </ul>
                                            </div>
                                            <!-- Coluna do Checklist -->
                                            <div class="md:w-2/3">
                                                <h4 class="font-semibold mb-4 text-orange-900">Modo de Preparo</h4>
                                                <div id="checklist-preparo-diurno" class="space-y-3">
                                                    <label class="flex items-start space-x-3">
                                                        <input type="checkbox" class="mt-1 h-4 w-4 text-orange-600 rounded border-gray-300 focus:ring-orange-500">
                                                        <span class="text-sm text-gray-700">Lave bem as mãos antes de começar</span>
                                                    </label>
                                                    <label class="flex items-start space-x-3">
                                                        <input type="checkbox" class="mt-1 h-4 w-4 text-orange-600 rounded border-gray-300 focus:ring-orange-500">
                                                        <span class="text-sm text-gray-700">Pegue um frasco de vidro âmbar limpo e seco de 10ml com tampa roll-on</span>
                                                    </label>
                                                    <label class="flex items-start space-x-3">
                                                        <input type="checkbox" class="mt-1 h-4 w-4 text-orange-600 rounded border-gray-300 focus:ring-orange-500">
                                                        <span class="text-sm text-gray-700">Adicione 4 gotas de óleo essencial de Bergamota no frasco</span>
                                                    </label>
                                                    <label class="flex items-start space-x-3">
                                                        <input type="checkbox" class="mt-1 h-4 w-4 text-orange-600 rounded border-gray-300 focus:ring-orange-500">
                                                        <span class="text-sm text-gray-700">Adicione 3 gotas de óleo essencial de Alecrim</span>
                                                    </label>
                                                    <label class="flex items-start space-x-3">
                                                        <input type="checkbox" class="mt-1 h-4 w-4 text-orange-600 rounded border-gray-300 focus:ring-orange-500">
                                                        <span class="text-sm text-gray-700">Adicione 2 gotas de óleo essencial de Hortelã-pimenta</span>
                                                    </label>
                                                    <label class="flex items-start space-x-3">
                                                        <input type="checkbox" class="mt-1 h-4 w-4 text-orange-600 rounded border-gray-300 focus:ring-orange-500">
                                                        <span class="text-sm text-gray-700">Complete o restante do frasco com óleo carreador de Amêndoas</span>
                                                    </label>
                                                    <label class="flex items-start space-x-3">
                                                        <input type="checkbox" class="mt-1 h-4 w-4 text-orange-600 rounded border-gray-300 focus:ring-orange-500">
                                                        <span class="text-sm text-gray-700">Feche bem o frasco e agite suavemente por 10 segundos</span>
                                                    </label>
                                                    <label class="flex items-start space-x-3">
                                                        <input type="checkbox" class="mt-1 h-4 w-4 text-orange-600 rounded border-gray-300 focus:ring-orange-500">
                                                        <span class="text-sm text-gray-700">Identifique o frasco com o nome da mistura e a data de preparo</span>
                                                    </label>
                                                </div>
                                                <div class="mt-4 p-3 bg-orange-50 rounded-lg border border-orange-100">
                                                    <p class="text-xs text-orange-800">💡 <strong>Dica:</strong> Guarde em local fresco e ao abrigo da luz solar direta. Use dentro de 3 meses.</p>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </details>

                                <details>
                                    <summary class="collapsible-strip flex justify-between items-center">
                                        <div class="flex items-center gap-3">
                                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
                                            </svg>
                                            <div>
                                                <div class="title">Como Funciona</div>
                                                <div class="subtitle">A ciência por trás dos óleos</div>
                                            </div>
                                        </div>
                                        <svg class="arrow w-5 h-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                                        </svg>
                                    </summary>
                                    <div class="collapsible-content space-y-4">
                                        <div class="mb-4">
                                            <h4 class="text-lg font-bold text-orange-900">Racional Terapêutico</h4>
                                            <p class="text-sm text-gray-700 mt-2">Esta sinergia foi cuidadosamente formulada para proporcionar energia sustentada e clareza mental ao longo do dia, utilizando óleos essenciais com propriedades estimulantes e melhoradoras do foco.</p>
                                        </div>
                                        
                                        <div class="space-y-6">
                                            <!-- Bergamota -->
                                            <div class="bg-orange-50 p-4 rounded-lg">
                                                <h5 class="font-bold text-orange-800 flex items-center gap-2">
                                                    <span class="inline-block w-2 h-2 rounded-full bg-amber-400"></span>
                                                    Bergamota (Citrus bergamia)
                                                </h5>
                                                <p class="text-sm text-orange-700 mt-1">Estudos indicam que o aroma cítrico da bergamota pode ajudar a reduzir o estresse e melhorar o humor, promovendo uma sensação de bem-estar sem causar sonolência.</p>
                                                <p class="text-xs text-orange-600 mt-2">Pesquisa publicada no Journal of Alternative and Complementary Medicine (2015)</p>
                                            </div>
                                            
                                            <!-- Alecrim -->
                                            <div class="bg-green-50 p-4 rounded-lg">
                                                <h5 class="font-bold text-green-800 flex items-center gap-2">
                                                    <span class="inline-block w-2 h-2 rounded-full bg-green-600"></span>
                                                    Alecrim (Rosmarinus officinalis)
                                                </h5>
                                                <p class="text-sm text-green-700 mt-1">O alecrim contém compostos como 1,8-cineol que demonstraram melhorar o desempenho cognitivo e o estado de alerta em estudos clínicos.</p>
                                                <p class="text-xs text-green-600 mt-2">Estudo publicado no Therapeutic Advances in Psychopharmacology (2012)</p>
                                            </div>
                                            
                                            <!-- Hortelã-pimenta -->
                                            <div class="bg-teal-50 p-4 rounded-lg">
                                                <h5 class="font-bold text-teal-800 flex items-center gap-2">
                                                    <span class="inline-block w-2 h-2 rounded-full bg-teal-400"></span>
                                                    Hortelã-pimenta (Mentha piperita)
                                                </h5>
                                                <p class="text-sm text-teal-700 mt-1">A inalação do óleo essencial de hortelã-pimenta demonstrou aumentar o estado de alerta e melhorar a memória em situações de fadiga mental.</p>
                                                <p class="text-xs text-teal-600 mt-2">Pesquisa publicada no International Journal of Neuroscience (2008)</p>
                                            </div>
                                        </div>
                                        
                                        <div class="mt-6 p-4 bg-white border border-orange-100 rounded-lg">
                                            <h5 class="font-semibold text-orange-800 mb-2">Mecanismo de Ação</h5>
                                            <p class="text-sm text-gray-700">A combinação desses óleos essenciais atua através da estimulação do sistema límbico, promovendo a liberação de neurotransmissores como a noradrenalina e a acetilcolina, que estão associados ao estado de alerta e à função cognitiva.</p>
                                        </div>
                                    </div>
                                </details>
                            </div>
                        </div>
                        
                        <!-- Protocolo Noturno (initially hidden) -->
                        <div id="protocol-noturno" class="protocol-card w-full max-w-lg sm:max-w-xl md:max-w-2xl lg:max-w-3xl flex-col hidden">
                            {{ ... existing noturno protocol card content ... }}
                        </div>
                    </section>
                </div>
            </div>

            <!-- Aba: Estudos Científicos -->
            <div id="studies" class="tab-content">
                <!-- Conteúdo de estudos científicos será adicionado aqui -->
            </div>

            <!-- Aba: Segurança -->
            <div id="security" class="tab-content">
                <div class="space-y-8">
                    <div class="info-card">
                        <div class="text-center mb-8">
                            <h1 class="text-3xl font-bold text-gray-800 mb-2 flex items-center gap-3">
                                <span class="text-3xl">🛡️</span>
                                Protocolo de Segurança Essencial
                            </h1>
                            <p class="text-gray-600">Leia com atenção antes de utilizar qualquer receita.</p>
                        </div>

                        <!-- Accordion de Segurança (Variação 3) -->
                        <div class="space-y-2">
                            <details class="bg-yellow-50 border-l-4 border-yellow-400 p-4 rounded-r-lg">
                                <summary class="font-bold text-yellow-900 cursor-pointer">1. Teste de Sensibilidade (Obrigatório)</summary>
                                <p class="mt-2 text-sm text-yellow-800">Antes do primeiro uso de qualquer nova mistura, realize este teste para prevenir reações alérgicas. É um passo crucial para a segurança.</p>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm mt-4">
                                    <div>
                                        <p class="font-semibold text-yellow-900 mb-2">Como Fazer:</p>
                                        <ol class="list-decimal list-inside text-yellow-900 space-y-2 pl-2">
                                            <li>Prepare uma pequena quantidade da mistura.</li>
                                            <li>Aplique uma gota na parte interna do antebraço.</li>
                                            <li>Cubra a área com um curativo e aguarde 24 horas.</li>
                                            <li>Após o período, remova o curativo e observe a pele.</li>
                                        </ol>
                                    </div>
                                    <div>
                                        <p class="font-semibold text-yellow-900 mb-2">O que Observar:</p>
                                        <ul class="list-disc list-inside text-yellow-900 space-y-2 pl-2">
                                            <li>Vermelhidão ou irritação</li>
                                            <li>Coceira intensa ou queimação</li>
                                            <li>Inchaço, bolhas ou urticária</li>
                                            <li>Qualquer forma de desconforto na área</li>
                                        </ul>
                                    </div>
                                </div>
                                <p class="text-xs text-yellow-700 mt-4"><strong>Resultado:</strong> Se notar qualquer uma dessas reações, não utilize a mistura. Lave a área com óleo vegetal (ex: coco, amêndoas) e depois com água e sabão.</p>
                            </details>
                            <details class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded-r-lg">
                                <summary class="font-bold text-blue-900 cursor-pointer">2. Uso Seguro e Armazenamento</summary>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-900 mt-2">
                                    <div>
                                        <h4 class="font-semibold mb-2">Diretrizes de Aplicação:</h4>
                                        <ul class="list-disc list-inside space-y-2 pl-2">
                                            <li><strong>Sempre dilua</strong> os óleos essenciais em um óleo carreador, conforme a receita. Nunca aplique puros na pele.</li>
                                            <li>Mantenha longe dos <strong>olhos, interior dos ouvidos e mucosas</strong>.</li>
                                            <li>Evite aplicar as misturas próximo ao rosto de crianças.</li>
                                            <li>O uso em crianças deve ser sempre <strong>supervisionado por um adulto</strong>.</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold mb-2">Armazenamento Correto:</h4>
                                        <ul class="list-disc list-inside space-y-2 pl-2">
                                            <li>Guarde em frascos de vidro escuro (âmbar ou azul).</li>
                                            <li>Mantenha em <strong>local fresco e ao abrigo da luz</strong> solar direta.</li>
                                            <li>Certifique-se de que as tampas estejam bem fechadas.</li>
                                            <li>Mantenha <strong>fora do alcance de crianças e animais</strong> de estimação.</li>
                                        </ul>
                                    </div>
                                </div>
                            </details>
                            <details class="bg-red-50 border-l-4 border-red-400 p-4 rounded-r-lg">
                                <summary class="font-bold text-red-900 cursor-pointer">3. Sinais de Alerta e Ação Necessária</summary>
                                <p class="mt-2 text-sm text-red-800">A aromaterapia é um suporte, mas não substitui a avaliação médica. Fique atento a estes sinais.</p>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm mt-4">
                                    <div>
                                        <h4 class="font-semibold text-red-900 mb-2">Procure Ajuda Médica Imediata se:</h4>
                                        <ul class="list-disc list-inside text-red-900 space-y-2 pl-2">
                                            <li>Ocorrer uma <strong>reação alérgica grave</strong> (inchaço no rosto ou garganta, urticária generalizada).</li>
                                            <li>Surgirem <strong>dificuldades respiratórias</strong> após o uso.</li>
                                            <li>Os sintomas que você está tratando piorarem drasticamente.</li>
                                        </ul>
                                    </div>
                                    <div>
                                        <h4 class="font-semibold text-red-900 mb-2">Consulte um Médico se a Condição Apresentar:</h4>
                                        <ul class="list-disc list-inside text-red-900 space-y-2 pl-2">
                                            <li>Dores de cabeça muito frequentes (&gt;3x/semana) ou incapacitantes.</li>
                                            <li>Febre associada aos sintomas.</li>
                                            <li>Vômitos recorrentes ou mudanças de comportamento.</li>
                                            <li>Qualquer sintoma severo, persistente ou preocupante.</li>
                                        </ul>
                                    </div>
                                </div>
                            </details>
                        </div>

                        <!-- Passo 4: Aviso Legal e de Responsabilidade -->
                        <div class="bg-gray-50 border-l-4 border-gray-400 rounded-r-lg p-6 shadow-sm">
                            <h3 class="text-xl font-bold text-gray-900 mb-3 flex items-center gap-2">
                                <span class="font-mono bg-gray-200 text-gray-800 rounded-full w-8 h-8 flex items-center justify-center text-lg">4</span>
                                Aviso Legal e de Responsabilidade
                            </h3>
                            <div class="space-y-3 text-sm text-gray-700">
                                <p><strong>Natureza Complementar:</strong> A aromaterapia é uma prática de bem-estar e não substitui diagnósticos, tratamentos ou conselhos médicos. As informações e receitas aqui contidas são para fins educacionais.</p>
                                <p><strong>Responsabilidade Individual:</strong> O uso dos óleos essenciais é de sua inteira responsabilidade. As reações podem variar. Monitore sempre as respostas do corpo e ajuste o uso ou suspenda-o se necessário.</p>
                                <p><strong>Busca Profissional:</strong> Consulte sempre um profissional de saúde qualificado (médico ou aromaterapeuta certificado) antes de iniciar o uso de óleos essenciais, especialmente em crianças, gestantes, lactantes ou pessoas com condições médicas preexistentes.</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        /**
         * Handles switching the main tabs ('Visão Geral', 'Receitas', etc.).
         */
        function switchTab(event, tabName) {
            document.querySelectorAll('.tab-content').forEach(content => {
                content.classList.remove('active');
            });
            document.querySelectorAll('.tab-button').forEach(button => {
                button.classList.remove('active');
            });
            document.getElementById(tabName).classList.add('active');
            if (event && event.currentTarget) {
                event.currentTarget.classList.add('active');
            }
        }

        /**
         * Handles switching protocol view inside the 'Receitas' tab.
         * This is called by the timeline items.
         */
        function switchProtocol(protocolName) {
            // Hide all protocol cards
            document.querySelectorAll('.protocol-card').forEach(card => {
                card.style.display = 'none';
            });

            // Show the selected protocol card
            document.getElementById('protocol-' + protocolName).style.display = 'flex';

            // Update active state for timeline items (dot and box highlight)
            document.querySelectorAll('.timeline-item').forEach(item => {
                item.classList.remove('active');
                item.classList.remove('active-box'); // Remove highlight from all
            });
            const activeItem = document.querySelector(`[data-protocol='${protocolName}']`);
            activeItem.classList.add('active');
            activeItem.classList.add('active-box'); // Add highlight to selected
        }

        /**
         * Handles navigating from 'Visão Geral' cards to the correct recipe.
         * This is called by the 'Ver receita' links on the flip cards.
         */
        function switchToRecipe(protocolId) {
            // Programmatically switch to the 'Receitas' tab
            const recipesTabButton = document.querySelector('.tab-button[onclick*="recipes"]');
            if (recipesTabButton) {
                switchTab({ currentTarget: recipesTabButton }, 'recipes');
            }
            
            // Use a timeout to ensure the tab switch is complete before switching protocol
            setTimeout(() => {
                switchProtocol(protocolId);
                // Scroll the recipes section into view for better UX
                document.getElementById('recipes').scrollIntoView({ behavior: 'smooth', block: 'start' });
            }, 50);

            // Close any open flip cards
            document.querySelectorAll('.flip-card-inner.is-flipped').forEach(card => {
                card.classList.remove('is-flipped');
            });

            return false; // Prevent default link behavior
        }
        
        // Function to initialize checklist progress tracking
        function initializeChecklist(checklistId, progressId) {
            const checklist = document.getElementById(checklistId);
            if (!checklist) return;

            const checkboxes = checklist.querySelectorAll('input[type="checkbox"]');
            const progressIndicator = document.getElementById(progressId);

            function updateChecklistProgress() {
                const total = checkboxes.length;
                if (total === 0) return;
                const checked = Array.from(checkboxes).filter(checkbox => checkbox.checked).length;
                const progress = Math.round((checked / total) * 100);

                if (progressIndicator) {
                    const progressBar = progressIndicator.querySelector('.progress-bar');
                    const progressText = progressIndicator.querySelector('.progress-text');
                    if (progressBar) {
                        progressBar.style.width = `${progress}%`;
                        progressBar.setAttribute('aria-valuenow', progress);
                    }
                    if (progressText) {
                        progressText.textContent = `${progress}%`;
                    }
                }
            }

            checkboxes.forEach(checkbox => {
                checkbox.addEventListener('change', updateChecklistProgress);
            });

            updateChecklistProgress(); // Initial update
        }

        /**
         * Manages the flip card behavior, ensuring only one card is flipped at a time.
         * Fix: toggles is-flipped on .flip-card (not .flip-card-inner) to match CSS.
         */
        function flipCard(button) {
            const card = button.closest('.flip-card');
            if (!card) return;

            // If this card is already flipped, just un-flip it.
            if (card.classList.contains('is-flipped')) {
                card.classList.remove('is-flipped');
                return;
            }

            // Un-flip any other card that is currently flipped.
            document.querySelectorAll('.flip-card.is-flipped').forEach(flippedCard => {
                flippedCard.classList.remove('is-flipped');
            });

            // Flip the target card.
            card.classList.add('is-flipped');
        }

        // Initialize everything when the DOM is fully loaded
        document.addEventListener('DOMContentLoaded', function() {
            // Set the default protocol view
            switchProtocol('matinal');

            // Initialize all checklists
            initializeChecklist('checklist-preparo-matinal', 'progress-indicator-matinal');
            initializeChecklist('checklist-preparo-diurno', 'progress-indicator-diurno');
            initializeChecklist('checklist-preparo-noturno', 'progress-indicator-noturno');
        });
    </script>

</body>
</html>
