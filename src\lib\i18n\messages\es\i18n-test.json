{"title": "Página de Prueba i18n Solo en Servidor", "description": "Esta página valida que el sistema de internacionalización solo en servidor está funcionando correctamente sin FOUC o incompatibilidades de hidratación.", "status": {"title": "✅ i18n Solo en Servidor Funcionando", "message": "Este contenido fue renderizado en el servidor con traducciones cargadas en el momento de construcción/renderización. No ocurrió ninguna carga de traducción del lado del cliente."}, "current-locale": "Idioma Actual", "timestamp": "Renderizado En", "features": {"server-only": {"title": "Renderización Solo en Servidor", "description": "Todas las traducciones se cargan y renderizan en el servidor usando React cache() para rendimiento optimizado."}, "no-fouc": {"title": "Sin FOUC", "description": "El contenido aparece instantáneamente en el idioma correcto sin ningún flash de contenido no traducido."}}, "view-examples": "<PERSON><PERSON>"}