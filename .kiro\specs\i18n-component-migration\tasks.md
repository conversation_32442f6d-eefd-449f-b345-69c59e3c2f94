# Implementation Plan

- [x] 1. Install and configure next-intl







  - Install next-intl package: `npm install next-intl`
  - Configure root layout with NextIntlClientProvider and getMessages
  - Update middleware to work with next-intl locale handling patterns
  - Verify existing translation files are accessible through next-intl
  - Test basic next-intl functionality with a simple component
  - _Requirements: 1.1, 1.2, 1.3, 1.4_




- [x] 2. Audit and document wrapper components





  - Find all files matching pattern `*-i18n.tsx` in the codebase
  - Document which components each wrapper wraps
  - Identify translation namespaces used by each wrapper
  - Determine if wrapped components can be server components or must be client components
  - Create migration priority list based on component complexity and usage

  - _Requirements: 2.1, 2.2_

  Wrapper Component Audit Results
Found 2 wrapper components:

src/features/homepage/layout/homepage-layout-i18n.tsx

Wraps: src/features/homepage/layout/homepage-layout.tsx (HomepageLayout)
Type: Client component wrapper with custom i18n context
Namespace: homepage
Migration complexity: High (creates custom context and provider)
Can be server component: No (uses client-side context)
src/features/homepage/components/hero-content/hero-content-i18n.tsx

Wraps: src/features/homepage/components/hero-content/hero-content.tsx (HeroContent)
Type: Client component using custom hook
Namespace: homepage
Migration complexity: Medium (uses custom useServerI18n hook)
Can be server component: No (uses framer-motion animations)
Key Observations:

Both original components already use useTranslations from next-intl
The wrapper components create a custom i18n context system
Both components must remain client components due to animations (framer-motion)
The wrappers are unnecessary since next-intl is already configured

  - Migrate src\features\homepage\layout\homepage-layout.tsx to be fully server next-intl getTranslations
  - Start with components that have no child components (leaf nodes)
  - For server components: Replace wrapper with direct `getTranslations` from next-intl/server
  - For client components: Replace wrapper with direct `useTranslations` from next-intl
  - Update translation key usage to match next-intl namespace patterns
  - Delete corresponding `-i18n.tsx` wrapper files
  - Update imports in parent components
  - _Requirements: 2.3, 2.4, 3.1, 3.2, 3.3_

Task 3 Summary
✅ Fixed the homepage error: Removed the undefined getAllTranslationsWithUserPreference function calls that were causing the runtime error.

✅ Migrated HomepageLayout to server-side next-intl:

Updated src/features/homepage/layout/homepage-layout.tsx to use getTranslations from next-intl/server
Made it an async server component that loads translations server-side
✅ Eliminated wrapper components:

Deleted src/features/homepage/layout/homepage-layout-i18n.tsx wrapper
Deleted src/features/homepage/components/hero-content/hero-content-i18n.tsx wrapper
Updated the index file to remove wrapper exports
✅ Verified existing next-intl usage: The HeroContent component was already correctly using useTranslations('homepage') from next-intl, so no changes were needed there.

- [ ] 4. Migrate complex components with children
  - Identify components that render other components as children
  - Determine optimal migration pattern (server component, client component, or props-based)
  - Apply chosen pattern and update translation usage
  - Test component functionality and translation display
  - Delete wrapper files and update imports
  - _Requirements: 2.3, 2.4, 3.1, 3.2, 3.3_

- [ ] 5. Replace custom hook usage throughout codebase
  - Find all usage of `use-i18n.ts` and replace with `useTranslations` from next-intl
  - Find all usage of `use-server-i18n.ts` and replace with `getTranslations` from next-intl/server
  - Update namespace patterns to match next-intl conventions
  - Ensure client components have access to NextIntlClientProvider context
  - Test all replaced hook usage works correctly
  - _Requirements: 4.1, 4.2, 4.3_

- [ ] 6. Handle complex cases with props-based translations
  - Identify components that need props-based translation approach
  - Update parent server components to load translations with `getTranslations`
  - Pass specific translations as props to client components
  - Update client components to receive and use translation props
  - Test complex translation scenarios work correctly
  - _Requirements: 3.3, 3.4_

- [ ] 7. Remove or deprecate custom i18n hooks
  - Mark `src/hooks/use-i18n.ts` as deprecated with clear migration instructions
  - Mark `src/hooks/use-server-i18n.ts` as deprecated with clear migration instructions
  - Add deprecation warnings to guide developers to next-intl patterns
  - Plan removal of custom hooks after all usage is migrated
  - Document migration path from custom hooks to next-intl hooks
  - _Requirements: 4.4_

- [ ] 8. Update bundle size and performance optimization
  - Remove client-side translation loading code to reduce bundle size
  - Eliminate custom i18n logic that's now handled by next-intl
  - Verify NextIntlClientProvider is efficiently providing translations
  - Test that server-side translation loading doesn't impact performance
  - Measure bundle size reduction from removing wrapper components
  - _Requirements: 5.1, 5.2, 5.4_

- [ ] 9. Test complete migration functionality
  - Test all migrated components render correctly in English, Portuguese, and Spanish
  - Verify no flash of untranslated content (FOUC) occurs
  - Test variable interpolation works correctly with next-intl patterns
  - Verify fallback behavior for missing translation keys
  - Test error scenarios display translated error messages
  - Confirm loading states show translated loading messages
  - Validate NextIntlClientProvider context works correctly for client components
  - _Requirements: 5.1, 5.2, 5.3, 6.1, 6.2, 6.3, 6.4, 6.5_

- [ ] 10. Validate performance and SEO improvements
  - Measure bundle size reduction from removing wrapper components
  - Verify server-side rendering includes translated content with next-intl
  - Test that search engines can index translated content
  - Confirm no client-side hydration issues occur with NextIntlClientProvider
  - Validate next-intl's built-in caching works properly
  - Test page load performance improvements from eliminating wrappers
  - _Requirements: 5.1, 5.2, 5.3, 5.4_

- [ ] 11. Clean up legacy code and wrapper files
  - Delete all `-i18n.tsx` wrapper files after successful migration
  - Remove or deprecate custom i18n hooks (`use-i18n.ts`, `use-server-i18n.ts`)
  - Update all import statements to reference direct components instead of wrappers
  - Remove unused translation loading code and client-side locale detection
  - Verify zero wrapper components remain in codebase
  - _Requirements: 2.2, 2.3, 2.4, 4.4_

- [ ] 12. Update documentation and validate migration
  - Update component documentation to reflect next-intl patterns
  - Document migration from wrapper approach to direct next-intl usage
  - Create guide for future components to use next-intl directly
  - Validate all requirements have been met and zero wrapper files remain
  - Perform final end-to-end testing across all locales with next-intl
  - Document benefits achieved: reduced bundle size, eliminated boilerplate, better performance
  - _Requirements: 2.4, 4.4, 5.4, 6.5_