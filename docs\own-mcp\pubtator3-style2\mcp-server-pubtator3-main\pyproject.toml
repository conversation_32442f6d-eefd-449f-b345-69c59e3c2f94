[project]
name = "mcp-server-pubtator3"
version = "0.2.1"
description = "PubTator3 API compatible with the MCP agent protocol"
readme = "README.md"
requires-python = ">=3.10"
license = "MIT AND (Apache-2.0 OR BSD-2-Clause)"
dependencies = [
    "aiohttp",
    "aiolimiter",
    "mcp[cli]",
]
authors = [
  {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
maintainers = [
  {name = "<PERSON><PERSON>", email = "<EMAIL>"}
]
[project.scripts]
mcp-server-pubtator3 = "mcp_server_pubtator3.__main__:main"


[project.urls]
Repository = "https://github.com/QIngyuanfl/mcp-server-pubtator3.git"
Issues = "https://github.com/QIngyuanfl/mcp-server-pubtator3/issues"
