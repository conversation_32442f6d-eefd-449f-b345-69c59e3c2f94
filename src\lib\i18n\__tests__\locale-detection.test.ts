/**
 * Comprehensive test suite for i18n locale detection and redirect logic
 * Tests all scenarios: authenticated/non-authenticated users, browser languages, fallbacks
 */

import { jest } from '@jest/globals';
import { redirect } from 'next/navigation';
import { headers } from 'next/headers';
import { redirectToUserPreferredLocale, getUserPreferredLocale } from '../user-preference-redirect';
import { getServerAuthState } from '@/features/auth/services/auth-state.service';
import { getCurrentUserProfile } from '@/features/user-auth-data/services/profile.service';

// Mock Next.js functions
jest.mock('next/navigation', () => ({
  redirect: jest.fn()
}));

jest.mock('next/headers', () => ({
  headers: jest.fn()
}));

// Mock auth services
jest.mock('@/features/auth/services/auth-state.service', () => ({
  getServerAuthState: jest.fn()
}));

jest.mock('@/features/user-auth-data/services/profile.service', () => ({
  getCurrentUserProfile: jest.fn()
}));

const mockRedirect = redirect as jest.MockedFunction<typeof redirect>;
const mockHeaders = headers as jest.MockedFunction<typeof headers>;
const mockGetServerAuthState = getServerAuthState as jest.MockedFunction<typeof getServerAuthState>;
const mockGetCurrentUserProfile = getCurrentUserProfile as jest.MockedFunction<typeof getCurrentUserProfile>;

describe('i18n Locale Detection and Redirect', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    // Reset environment
    process.env.NODE_ENV = 'test';
  });

  describe('Browser Language Detection', () => {
    beforeEach(() => {
      // Mock non-authenticated user
      mockGetServerAuthState.mockResolvedValue({ user: null });
    });

    it('should detect Portuguese from pt-BR browser', async () => {
      const mockHeadersList = new Map([
        ['accept-language', 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7']
      ]);
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/pt');
    });

    it('should detect Spanish from es-ES browser', async () => {
      const mockHeadersList = new Map([
        ['accept-language', 'es-ES,es;q=0.9,en;q=0.8']
      ]);
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/es');
    });

    it('should detect English from en-US browser', async () => {
      const mockHeadersList = new Map([
        ['accept-language', 'en-US,en;q=0.9']
      ]);
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/en');
    });

    it('should fallback to English for unsupported language', async () => {
      const mockHeadersList = new Map([
        ['accept-language', 'fr-FR,fr;q=0.9,de;q=0.8']
      ]);
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/en');
    });

    it('should handle malformed Accept-Language header', async () => {
      const mockHeadersList = new Map([
        ['accept-language', 'invalid-header-format']
      ]);
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/en');
    });

    it('should fallback to English when no Accept-Language header', async () => {
      const mockHeadersList = new Map();
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/en');
    });
  });

  describe('Authenticated User Preferences', () => {
    const mockUser = { id: 'user-123' };

    beforeEach(() => {
      mockGetServerAuthState.mockResolvedValue({ user: mockUser });
      const mockHeadersList = new Map([
        ['accept-language', 'pt-BR,pt;q=0.9,en;q=0.8'] // Browser prefers Portuguese
      ]);
      mockHeaders.mockResolvedValue(mockHeadersList as any);
    });

    it('should use user database preference over browser language', async () => {
      mockGetCurrentUserProfile.mockResolvedValue({
        id: 'profile-123',
        language: 'es' // User prefers Spanish
      } as any);

      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/es');
    });

    it('should fallback to browser language when user has no language preference', async () => {
      mockGetCurrentUserProfile.mockResolvedValue({
        id: 'profile-123',
        language: null
      } as any);

      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/pt'); // From browser
    });

    it('should fallback to browser language when user has invalid language preference', async () => {
      mockGetCurrentUserProfile.mockResolvedValue({
        id: 'profile-123',
        language: 'invalid-lang'
      } as any);

      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/pt'); // From browser
    });

    it('should handle database errors gracefully', async () => {
      mockGetCurrentUserProfile.mockRejectedValue(new Error('Database error'));

      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/pt'); // Fallback to browser
    });
  });

  describe('Path Handling', () => {
    beforeEach(() => {
      mockGetServerAuthState.mockResolvedValue({ user: null });
      const mockHeadersList = new Map([
        ['accept-language', 'pt-BR,pt;q=0.9']
      ]);
      mockHeaders.mockResolvedValue(mockHeadersList as any);
    });

    it('should handle root path correctly', async () => {
      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/pt');
    });

    it('should handle nested paths correctly', async () => {
      await redirectToUserPreferredLocale('/dashboard');

      expect(mockRedirect).toHaveBeenCalledWith('/pt/dashboard');
    });

    it('should handle paths with query parameters', async () => {
      await redirectToUserPreferredLocale('/search?q=test');

      expect(mockRedirect).toHaveBeenCalledWith('/pt/search?q=test');
    });
  });

  describe('Error Handling', () => {
    it('should fallback to English on critical errors', async () => {
      mockGetServerAuthState.mockRejectedValue(new Error('Critical error'));
      mockHeaders.mockRejectedValue(new Error('Headers error'));

      await redirectToUserPreferredLocale('/');

      expect(mockRedirect).toHaveBeenCalledWith('/en');
    });

    it('should use custom fallback locale', async () => {
      mockGetServerAuthState.mockRejectedValue(new Error('Critical error'));
      mockHeaders.mockRejectedValue(new Error('Headers error'));

      await redirectToUserPreferredLocale('/', 'pt');

      expect(mockRedirect).toHaveBeenCalledWith('/pt');
    });
  });

  describe('getUserPreferredLocale (non-redirecting)', () => {
    it('should return user preference without redirecting', async () => {
      const mockUser = { id: 'user-123' };
      mockGetServerAuthState.mockResolvedValue({ user: mockUser });
      mockGetCurrentUserProfile.mockResolvedValue({
        id: 'profile-123',
        language: 'es'
      } as any);

      const result = await getUserPreferredLocale();

      expect(result).toBe('es');
      expect(mockRedirect).not.toHaveBeenCalled();
    });

    it('should return browser locale for non-authenticated users', async () => {
      mockGetServerAuthState.mockResolvedValue({ user: null });
      const mockHeadersList = new Map([
        ['accept-language', 'pt-BR,pt;q=0.9']
      ]);
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      const result = await getUserPreferredLocale();

      expect(result).toBe('pt');
      expect(mockRedirect).not.toHaveBeenCalled();
    });

    it('should return English fallback on errors', async () => {
      mockGetServerAuthState.mockRejectedValue(new Error('Error'));
      // Clear headers to simulate no browser language available
      const mockHeadersList = new Map();
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      const result = await getUserPreferredLocale();

      expect(result).toBe('en');
      expect(mockRedirect).not.toHaveBeenCalled();
    });
  });

  describe('Priority System Integration', () => {
    it('should follow correct priority: User DB > Browser > Default', async () => {
      // Test sequence: authenticated user with preference
      const mockUser = { id: 'user-123' };
      mockGetServerAuthState.mockResolvedValue({ user: mockUser });
      mockGetCurrentUserProfile.mockResolvedValue({
        language: 'es' // User prefers Spanish
      } as any);
      
      // Browser prefers Portuguese
      const mockHeadersList = new Map([
        ['accept-language', 'pt-BR,pt;q=0.9']
      ]);
      mockHeaders.mockResolvedValue(mockHeadersList as any);

      await redirectToUserPreferredLocale('/');

      // Should use user preference (es) over browser preference (pt)
      expect(mockRedirect).toHaveBeenCalledWith('/es');
    });
  });
});
