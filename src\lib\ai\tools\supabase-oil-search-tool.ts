/**
 * @fileoverview Tool for searching essential oils in Supabase using vector embeddings.
 *
 * This module provides a tool for the oil enrichment agent. It takes an essential oil's
 * names, generates a combined embedding for them, and then uses that embedding to
 * perform a vector similarity search against a Supabase database via an RPC function.
 */

import { tool } from '@openai/agents';
import { z } from 'zod';
import { getEmbeddingsService } from '../services/unified-embeddings.service';
import * as _ from 'lodash';
import { createClient } from '@supabase/supabase-js';

// Zod schema for the tool's input parameters
const SupabaseOilSearchParams = z.object({
  oil_id: z.string().describe("The original ID of the oil being searched."),
  name_english: z.string().describe("The English name of the essential oil (e.g., 'Lavender')."),
  name_botanical: z.string().describe("The botanical name of the essential oil (e.g., '<PERSON>van<PERSON>la angustifolia')."),
});

// Type definition for the expected structure of a single search result from Supabase
interface SupabaseOilResult {
    id: string;
    name_english: string;
    name_scientific: string;
    name_portuguese: string;
    general_description?: string;
    image_url?: string;
    names_concatenated?: string;
    
    // Safety IDs from database
    internal_use_status_id?: string;
    dilution_recommendation_id?: string;
    phototoxicity_status_id?: string;
    
    // Direct JSON objects for safety information
    internal_use: {
        name?: string | null;
        code?: string | null;
        description?: string | null;
        guidance?: string | null;
    };
    
    dilution: {
        name?: string | null;
        description?: string | null;
        percentage_max?: number | null;
        percentage_min?: number | null;
        ratio?: string | null;
    };
    
    phototoxicity: {
        status?: string | null;
        guidance?: string | null;
        description?: string | null;
    };
    
    // Arrays for multi-value safety information
    pregnancy_nursing_safety: Array<{
        id?: string | null;
        name?: string | null;
        status_description?: string | null;
        code?: string | null;
        usage_guidance?: string | null;
        description?: string | null;
    }>;
    
    child_safety: Array<{
        age_range_id?: string | null;
        age_range?: string | null;
        safety_notes?: string | null;
    }>;
    
    created_at?: string;
    updated_at?: string;
    similarity: number;
}

/**
 * Helper function to log a sample of the embedding vector
 * @param embedding The embedding vector to log a sample of
 */
function logEmbeddingSample(embedding: number[]) {
  if (!embedding || embedding.length === 0) {
    console.log('❌ [Embedding Verification] No embedding generated!');
    return;
  }
  
  const sampleSize = 5;
  const embeddingLength = embedding.length;
  const sample = embedding.slice(0, sampleSize);
  
  console.log(`📊 [Embedding Verification] Vector length: ${embeddingLength}`);
  console.log(`📊 [Embedding Verification] First ${sampleSize} values: [${sample.map(v => v.toFixed(6)).join(', ')}]`);
  
  // Basic statistical checks
  const sum = embedding.reduce((acc, val) => acc + val, 0);
  const mean = sum / embeddingLength;
  const nonZeroCount = embedding.filter(v => v !== 0).length;
  const nonZeroPercentage = (nonZeroCount / embeddingLength) * 100;
  
  console.log(`📊 [Embedding Verification] Mean value: ${mean.toFixed(6)}`);
  console.log(`📊 [Embedding Verification] Non-zero values: ${nonZeroPercentage.toFixed(2)}%`);
  
  // Verify the embedding is normalized (should have a magnitude close to 1.0)
  const magnitude = Math.sqrt(embedding.reduce((acc, val) => acc + val * val, 0));
  console.log(`📊 [Embedding Verification] Vector magnitude: ${magnitude.toFixed(6)} (should be close to 1.0)`);
}

/**
 * Creates a Supabase client for use in the tool
 * @returns A Supabase client instance
 */
function getSupabaseClient() {
  const supabaseUrl = process.env['NEXT_PUBLIC_SUPABASE_URL'];
  const supabaseKey = process.env['NEXT_PUBLIC_SUPABASE_ANON_KEY'];
  
  if (!supabaseUrl || !supabaseKey) {
    throw new Error('Supabase URL and anon key environment variables are required');
  }
  
  return createClient(supabaseUrl, supabaseKey);
}

/**
 * Performs a vector search for an essential oil in Supabase.
 *
 * @param oilId The original ID of the oil.
 * @param englishName The English name of the oil.
 * @param botanicalName The botanical name of the oil.
 * @returns The enriched oil data from the vector search.
 */
async function searchSupabase(oilId: string, englishName: string, botanicalName: string) {
  try {
    // 1. Get embedding for the combined oil names
    console.log(`🔍 [Supabase Search] Generating embedding for: "${englishName} (${botanicalName})"...`);
    console.log(`🔍 [Supabase Search] Using embedding model: text-embedding-3-small (Supabase-specific)`);
    const embeddingsService = getEmbeddingsService();
    const searchText = `${englishName} - ${botanicalName}`;
    
    console.log(`🔄 [Embedding Request] Starting embedding generation for text: "${searchText}"`);
    
    const startTime = Date.now();
    const embeddingResponse = await embeddingsService.createSingleEmbedding({ 
      text: searchText,
      model: 'text-embedding-3-small'  // Use text-embedding-3-small for Supabase
    });
    const endTime = Date.now();
    
    const queryEmbedding = embeddingResponse.embedding;
    console.log(`✅ [Supabase Search] Embedding generated in ${endTime - startTime}ms.`);
    console.log(`✅ [Embedding Response] Model used: ${embeddingResponse.model}`);
    console.log(`✅ [Embedding Response] Tokens used: ${embeddingResponse.usage.total_tokens}`);
    
    // Verify the embedding
    logEmbeddingSample(queryEmbedding);

    // 2. Call Supabase RPC function using the client
    console.log(`🚀 [Supabase Search] Performing vector search via RPC...`);
    
    // Initialize Supabase client
    const supabase = getSupabaseClient();
    
    // RPC parameters
    const match_threshold = 0.5; // Lower threshold to increase chances of finding matches
    const match_count = 1;      // We want the single best match
    
    console.log(`🚀 [Supabase Search] Parameters: match_threshold=${match_threshold}, match_count=${match_count}`);
    
    const requestStartTime = Date.now();
    // Use the new match_oils_with_safety_ids function that works with the view
    const rpcResponse = await supabase.rpc('match_oils_with_safety_ids', {
      query_embedding: queryEmbedding,
      match_threshold,
      match_count
    });
    const requestEndTime = Date.now();
    
    console.log(`🚀 [Supabase Search] Request completed in ${requestEndTime - requestStartTime}ms.`);
    
    // Print the raw response for debugging
    console.log('📋 [Supabase Search] Raw RPC response:');
    console.log(JSON.stringify(rpcResponse, null, 2));
    
    const { data, error } = rpcResponse;
    
    if (error) {
      console.error(`❌ [Supabase Search] RPC error:`, error);
      throw new Error(`Supabase RPC request failed: ${error.message}`);
    }
    
    console.log(`✅ [Supabase Search] Raw data type: ${typeof data}`);
    
    // Ensure results is an array with proper validation
    let resultsArray: SupabaseOilResult[] = [];
    
    if (data === null) {
      console.log(`ℹ️ [Supabase Search] Data is null`);
    } else if (Array.isArray(data)) {
      console.log(`✅ [Supabase Search] Data is an array with ${data.length} items`);
      resultsArray = data;
    } else if (typeof data === 'object' && data !== null) {
      console.log(`✅ [Supabase Search] Data is an object, converting to array`);
      resultsArray = [data as SupabaseOilResult];
    } else {
      console.log(`⚠️ [Supabase Search] Data is in unexpected format: ${typeof data}`);
    }
    
    console.log(`✅ [Supabase Search] Processed ${resultsArray.length} result(s).`);
    
    if (resultsArray.length === 0) {
      console.log(`ℹ️ [Supabase Search] No matches found for "${searchText}"`);
      return {
        status: 'No match found',
        original_oil_id: oilId,
        search_term: searchText,
      };
    } 
    
    // We know resultsArray[0] exists because we just checked the length above
    // Using non-null assertion because we've verified the array is not empty
    const bestMatch = resultsArray[0]!;
    
    console.log(`✅ [Supabase Search] Found match: ${bestMatch.name_english} (${bestMatch.name_scientific || bestMatch.name_portuguese})`);
    console.log(`✅ [Supabase Search] Similarity score: ${bestMatch.similarity.toFixed(4)}`);
    
    // 3. Aggregate the data with all relevant fields
    const enrichedData = {
      // Supabase data
      supabase_id: bestMatch.id,
      name_english: bestMatch.name_english,
      name_scientific: bestMatch.name_scientific || bestMatch.name_portuguese,
      
      // Basic information
      general_description: bestMatch.general_description || null,
      image_url: bestMatch.image_url || null,
      names_concatenated: bestMatch.names_concatenated || null,
      
      // Safety information with simplified nested structure
      safety: {
        // Safety arrays directly from the database
        internal_use: bestMatch.internal_use || {},
        dilution: bestMatch.dilution || {},
        phototoxicity: bestMatch.phototoxicity || {},
        pregnancy_nursing: bestMatch.pregnancy_nursing_safety || [],
        child_safety: bestMatch.child_safety || []
      },
      
      // Safety IDs
      internal_use_status_id: bestMatch.internal_use_status_id || null,
      dilution_recommendation_id: bestMatch.dilution_recommendation_id || null,
      phototoxicity_status_id: bestMatch.phototoxicity_status_id || null,
      pregnancy_nursing_ids: bestMatch.pregnancy_nursing_safety?.map((item: any) => item.id).filter(Boolean) || [],
      child_safety_ids: bestMatch.child_safety?.map((item: any) => item.age_range_id).filter(Boolean) || [],
      
      // Original data and metadata
      original_oil_id: oilId,
      original_name_english: englishName,
      original_name_scientific: botanicalName,
      similarity_score: bestMatch.similarity,
      embedding_model: embeddingResponse.model,
      
      // Timestamps
      created_at: bestMatch.created_at || null,
      updated_at: bestMatch.updated_at || null
    };

    console.log('🔄 [Supabase Search] Aggregated enriched data.');
    return enrichedData;

  } catch (error) {
    const message = error instanceof Error ? error.message : String(error);
    console.error('❌ [Supabase Search] Tool failed:', message);
    
    if (error instanceof Error && error.stack) {
      console.error('❌ [Supabase Search] Stack trace:', error.stack);
    }
    
    throw new Error(`Supabase oil search failed: ${message}`);
  }
}

/**
 * OpenAI Agents JS tool to enrich an essential oil's data using Supabase vector search.
 */
export const supabaseOilSearchTool = tool({
  name: 'enrich_oil_with_vector_search',
  description: `For a single essential oil, this tool generates an embedding from its names,
  performs a vector search in the Supabase database to find the definitive version of the oil,
  and returns its aggregated, enriched data including human-readable safety information.`,
  
  parameters: SupabaseOilSearchParams,
  
  execute: async (args) => {
    try {
      const { oil_id, name_english, name_botanical } = args;
      console.log(`🛠️ [Supabase Search Tool] Enriching oil: ${name_english}`);
      
      const result = await searchSupabase(oil_id, name_english, name_botanical);
      
      return JSON.stringify(result, null, 2);

    } catch (error) {
      const message = error instanceof Error ? error.message : 'Unknown error';
      console.error('❌ [Supabase Search Tool] Execution error:', message);
      return JSON.stringify({
          error: true,
          message: `Tool execution failed: ${message}`,
          original_args: args,
      }, null, 2);
    }
  }
}); 