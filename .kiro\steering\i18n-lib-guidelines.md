---
inclusion: fileMatch
fileMatchPattern: 'src/lib/i18n/**/*'
---

# i18n Library Guidelines

This steering file provides specific guidelines for working with the `src/lib/i18n` directory and its internationalization system.

## Architecture Overview

The i18n system is **server-first** and **middleware-driven** with these core principles:
- Single source of truth for locale detection and routing
- Server-side rendering for optimal SEO and performance
- Edge runtime compatibility for global performance
- Type-safe translations with strict TypeScript support

## File Structure and Organization

```
src/lib/i18n/
├── server.ts                          # Server-side translation utilities
├── request.ts                         # next-intl configuration
├── user-preference-redirect.ts        # User preference handling
├── index.ts                          # Public API exports
├── types/i18n.ts                     # TypeScript definitions
├── utils/
│   ├── language-utils.ts             # Client-side utilities
│   └── server-language-utils.ts      # Server-side utilities
├── messages/                         # Translation files
│   ├── en/                          # English (default)
│   ├── pt/                          # Portuguese
│   └── es/                          # Spanish
├── prompts/                         # AI translation prompts
└── __tests__/                       # Test files
```

## Core Functions and Usage Patterns

### Server-Side Functions (Primary)

Always prefer server-side functions for optimal performance and SEO:

```typescript
// Get optimal locale with user preference integration
const locale = await getOptimalLocale();

// Create translator with user preferences
const t = await createTranslatorWithUserPreference('namespace');

// Generate SEO metadata with hreflang links
const metadata = await generateSEOMetadata('namespace', 'page');

// Load translations directly
const translations = await getTranslations('namespace', 'pt');
```

### Translation File Patterns

#### Namespace Structure
- Use descriptive namespace names matching feature areas
- Keep namespaces focused and cohesive
- Follow kebab-case for namespace files

#### Key Naming Conventions
- Use `snake_case` for all translation keys
- Structure hierarchically with dots for nested access
- Use `meta_` prefix for SEO metadata keys
- Be descriptive and specific

```json
{
  "title": "Feature Title",
  "subtitle": "Feature subtitle",
  "actions": {
    "create": "Create New",
    "edit": "Edit",
    "delete": "Delete"
  },
  "meta_title": "Feature - App Name",
  "meta_description": "SEO description for feature"
}
```

## Supported Locales

The system supports these locales:
- `en` - English (default/fallback)
- `pt` - Portuguese (Brazil)
- `es` - Spanish (Latin America)

## Development Guidelines

### Adding New Features with i18n

1. **Create translation files for all locales**:
```bash
touch src/lib/i18n/messages/en/feature-name.json
touch src/lib/i18n/messages/pt/feature-name.json
touch src/lib/i18n/messages/es/feature-name.json
```

2. **Use server components with translations**:
```typescript
export default async function FeaturePage() {
  const t = await createTranslatorWithUserPreference('feature-name');
  
  return (
    <div>
      <h1>{t('title')}</h1>
      <p>{t('description')}</p>
    </div>
  );
}
```

3. **Add SEO metadata**:
```typescript
export async function generateMetadata() {
  return await generateSEOMetadata('feature-name', 'main');
}
```

### Best Practices

#### DO:
- Always use server components for i18n when possible
- Use descriptive translation keys (`user_profile_edit_button` not `btn1`)
- Group related translations in nested objects
- Include SEO metadata for all public pages
- Test all supported locales before deployment
- Use the `cache()` wrapper for performance optimization

#### DON'T:
- Use client-side locale detection (middleware handles this)
- Create duplicate locale detection logic
- Use camelCase for translation keys
- Skip translation files for any supported locale
- Hardcode text strings in components

### Error Handling

The system automatically handles missing translations:
1. **Missing key**: Returns the key as fallback
2. **Missing file**: Falls back to English
3. **Missing English**: Returns empty object

### Type Safety

Always use the provided TypeScript types:

```typescript
import type { 
  SupportedLocale, 
  TranslationFunction, 
  SEOMetadata 
} from '@/lib/i18n/types/i18n';
```

## Common Patterns

### Server Component with Translations
```typescript
// ✅ Recommended pattern
export default async function Page() {
  const t = await createTranslatorWithUserPreference('homepage');
  return <h1>{t('welcome_title')}</h1>;
}
```

### Nested Translation Keys
```typescript
// ✅ Good: Hierarchical structure
t('user.profile.edit_button')
t('actions.save')
t('messages.success.created')

// ❌ Avoid: Flat keys
t('userProfileEditButton')
t('saveAction')
t('createdSuccessMessage')
```

### Variable Interpolation
```json
{
  "welcome_message": "Welcome back, {{username}}!",
  "progress": "Step {{current}} of {{total}}"
}
```

```typescript
const message = t('welcome_message', { username: 'John' });
const progress = t('progress', { current: 2, total: 5 });
```

## Performance Considerations

### Server-First Benefits
- Better SEO with fully translated content
- No FOUC (Flash of Untranslated Content)
- Faster loading without client-side translation loading
- Better Core Web Vitals scores

### Caching
All translation functions use React `cache()` for optimal performance and are automatically cached per request.

### Edge Runtime
Middleware runs on Edge Runtime for:
- Faster redirects worldwide
- Lower latency for locale detection
- Better scalability

## Testing Guidelines

### Manual Testing Checklist
- [ ] Root URL (`/`) redirects to user's preferred locale
- [ ] Direct locale URLs (`/pt`, `/en`, `/es`) work correctly
- [ ] Authenticated users get their database language preference
- [ ] Non-authenticated users get browser language detection
- [ ] SEO metadata appears in correct language
- [ ] Hreflang links are generated correctly

### Debug Mode
Enable detailed logging in development by checking `NODE_ENV === 'development'` in i18n functions.

## Migration Guidelines

When updating existing i18n code:
1. Remove client-side locale detection
2. Replace `useI18n` hooks with server-side functions
3. Move translations to namespace structure
4. Update translation keys to use snake_case
5. Add SEO metadata for public pages
6. Convert to server components where possible

## Security Considerations

- All locale inputs are validated against supported locales
- Invalid locales fall back to default ('en')
- Translation keys are escaped by default
- Variable interpolation is safe
- No dynamic translation key generation from user input

## File References

Key files in the i18n system:
- #[[file:middleware.ts]] - Single source of truth for redirects
- #[[file:src/lib/i18n/server.ts]] - Server-side utilities
- #[[file:src/lib/i18n/request.ts]] - next-intl configuration
- #[[file:src/lib/i18n/types/i18n.ts]] - TypeScript definitions