/**
 * Locale-specific layout for App Router
 * Provides locale validation and context without conflicting with root layout
 */

import { notFound } from 'next/navigation';
import { NextIntlClientProvider } from 'next-intl';
import { getMessages } from 'next-intl/server';
import { LocaleSetter } from '@/components/locale-setter';

const locales = ['en', 'pt', 'es'];

interface LocaleLayoutProps {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
}

export default async function LocaleLayout({
  children,
  params,
}: LocaleLayoutProps) {
  const { locale } = await params;

  // Validate locale
  if (!locales.includes(locale)) {
    notFound();
  }

  // Load messages for next-intl
  const messages = await getMessages();

  // Return children wrapped with NextIntlClientProvider for next-intl support
  return (
    <>
      <LocaleSetter locale={locale} />
      <NextIntlClientProvider messages={messages}>
        <div className={`locale-${locale}`} data-locale={locale}>
          {children}
        </div>
      </NextIntlClientProvider>
    </>
  );
}

export async function generateStaticParams() {
  return locales.map((locale) => ({ locale }));
}
