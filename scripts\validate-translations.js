/**
 * Validate translation files for consistency and completeness
 * Ensures all languages have the same keys for SEO consistency
 */

const fs = require('fs');
const path = require('path');

const locales = ['en', 'pt', 'es'];
const namespaces = ['common', 'auth', 'create-recipe', 'dashboard', 'homepage'];

function getAllKeys(obj, prefix = '') {
  let keys = [];
  
  for (const [key, value] of Object.entries(obj)) {
    if (key.startsWith('_')) continue; // Skip metadata
    
    const fullKey = prefix ? `${prefix}.${key}` : key;
    
    if (typeof value === 'object' && value !== null) {
      keys.push(...getAllKeys(value, fullKey));
    } else {
      keys.push(fullKey);
    }
  }
  
  return keys.sort();
}

function validateTranslations() {
  console.log('🔍 Validating translation files...\n');
  
  let hasErrors = false;
  
  for (const namespace of namespaces) {
    console.log(`📁 Checking namespace: ${namespace}`);
    
    const localeKeys = {};
    
    // Load all locale files for this namespace
    for (const locale of locales) {
      const filePath = path.join(process.cwd(), 'src', 'lib', 'i18n', 'messages', locale, `${namespace}.json`);
      
      if (!fs.existsSync(filePath)) {
        console.log(`❌ Missing file: ${filePath}`);
        hasErrors = true;
        continue;
      }
      
      try {
        const content = JSON.parse(fs.readFileSync(filePath, 'utf8'));
        localeKeys[locale] = getAllKeys(content);
      } catch (error) {
        console.log(`❌ Invalid JSON in ${filePath}: ${error.message}`);
        hasErrors = true;
      }
    }
    
    // Compare keys between locales
    const baseKeys = localeKeys['en'] || [];
    
    for (const locale of locales) {
      if (!localeKeys[locale]) continue;
      
      const currentKeys = localeKeys[locale];
      const missingKeys = baseKeys.filter(key => !currentKeys.includes(key));
      const extraKeys = currentKeys.filter(key => !baseKeys.includes(key));
      
      if (missingKeys.length > 0) {
        console.log(`❌ ${locale}: Missing keys:`, missingKeys);
        hasErrors = true;
      }
      
      if (extraKeys.length > 0) {
        console.log(`⚠️  ${locale}: Extra keys:`, extraKeys);
      }
      
      if (missingKeys.length === 0 && extraKeys.length === 0) {
        console.log(`✅ ${locale}: All keys match`);
      }
    }
    
    console.log('');
  }
  
  if (hasErrors) {
    console.log('❌ Translation validation failed!');
    process.exit(1);
  } else {
    console.log('✅ All translations are valid!');
  }
}

function generateMissingTranslations() {
  console.log('🔧 Generating missing translation templates...\n');
  
  for (const namespace of namespaces) {
    const enFilePath = path.join(process.cwd(), 'src', 'lib', 'i18n', 'messages', 'en', `${namespace}.json`);
    
    if (!fs.existsSync(enFilePath)) continue;
    
    const enContent = JSON.parse(fs.readFileSync(enFilePath, 'utf8'));
    
    for (const locale of ['pt', 'es']) {
      const localeFilePath = path.join(process.cwd(), 'src', 'lib', 'i18n', 'messages', locale, `${namespace}.json`);
      
      if (!fs.existsSync(localeFilePath)) {
        // Create template with TRANSLATE prefixes
        const template = addTranslatePrefixes(enContent);
        fs.writeFileSync(localeFilePath, JSON.stringify(template, null, 2));
        console.log(`✅ Created template: ${localeFilePath}`);
      }
    }
  }
}

function addTranslatePrefixes(obj) {
  const result = {};
  
  for (const [key, value] of Object.entries(obj)) {
    if (key.startsWith('_')) {
      result[key] = value; // Keep metadata as-is
    } else if (typeof value === 'object' && value !== null) {
      result[key] = addTranslatePrefixes(value);
    } else {
      result[key] = `TRANSLATE: ${value}`;
    }
  }
  
  return result;
}

// Run validation
const command = process.argv[2];

if (command === 'generate') {
  generateMissingTranslations();
} else {
  validateTranslations();
}