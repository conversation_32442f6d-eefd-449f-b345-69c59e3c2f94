{"title": "Página de Teste i18n Apenas no Servidor", "description": "Esta página valida que o sistema de internacionalização apenas no servidor está funcionando corretamente sem FOUC ou incompatibilidades de hidratação.", "status": {"title": "✅ i18n Apenas no Servidor Funcionando", "message": "Este conteúdo foi renderizado no servidor com traduções carregadas no momento da construção/renderização. Nenhum carregamento de tradução do lado do cliente ocorreu."}, "current-locale": "Idioma Atual", "timestamp": "Renderizado Em", "features": {"server-only": {"title": "Renderização Apenas no Servidor", "description": "<PERSON><PERSON> as traduç<PERSON><PERSON> são carregadas e renderizadas no servidor usando React cache() para performance otimizada."}, "no-fouc": {"title": "Sem FOUC", "description": "O conteúdo aparece instantaneamente no idioma correto sem qualquer flash de conteúdo não traduzido."}}, "view-examples": "Ver Exemplos"}