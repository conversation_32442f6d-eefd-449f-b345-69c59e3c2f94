{"title": "Server-Only i18n Examples", "description": "Demonstration of server-side translation patterns with Client Components that receive translations as props, eliminating FOUC and hydration mismatches.", "examples": {"client-button": {"title": "<PERSON><PERSON> with Translations", "description": "Shows how Client Components can receive translations as props from Server Components.", "code-title": "Implementation Pattern:"}, "locale-switcher": {"title": "Locale Switcher", "description": "Language switcher that works without router dependencies or client-side translation loading.", "code-title": "Key Features:"}, "interactive-form": {"title": "Interactive Form with Validation", "description": "Complex form with client-side validation using server-provided translations.", "code-title": "Server Action Pattern:"}}, "button": {"text": "Click Me", "loading": "Loading..."}, "locale-switcher": {"select-language": "Select Language", "english": "English", "portuguese": "Portuguese", "spanish": "Spanish"}, "form": {"name-label": "Full Name", "name-placeholder": "Enter your full name", "name-required": "Name is required", "email-label": "Email Address", "email-placeholder": "Enter your email address", "email-required": "Email is required", "email-invalid": "Please enter a valid email address", "message-label": "Message", "message-placeholder": "Enter your message", "message-required": "Message is required", "submit-button": "Send Message", "submitting": "Sending...", "submit-error": "Failed to send message. Please try again."}, "benefits": {"title": "Benefits of Server-Only i18n", "no-fouc": {"title": "No FOUC", "description": "Content appears instantly in the correct language"}, "performance": {"title": "Performance", "description": "React cache() eliminates redundant loading"}, "seo": {"title": "Perfect SEO", "description": "Search engines see fully translated content"}, "hydration": {"title": "No Hydration Issues", "description": "Server and client content always match"}}}