/**
 * @fileoverview Causes Selection component for Essential Oil Recipe Creator.
 * Fetches and displays potential causes for user selection with validation.
 * OPTIMIZED: Uses centralized streaming utilities to eliminate redundant data transformation logic.
 */

'use client';

import React, { useState, useEffect, useCallback, useRef, useMemo } from 'react';
import { useRecipeStore } from '../store/recipe-store';
import { useRecipeWizardNavigation } from '../hooks/use-recipe-navigation';
import type { PotentialCause } from '../types/recipe.types';
import { cn } from '@/lib/utils';
import { useAIStreaming } from '@/lib/ai/hooks/use-ai-streaming';
import AIStreamingModal from '@/components/ui/ai-streaming-modal';
import { Button } from '@/components/ui/button';
import { Loader2 } from 'lucide-react';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { createStreamRequest } from '../utils/api-data-transform';
import { transformRecipeWizardData, extractFinalStreamingData } from '@/lib/ai/utils/streaming-utils';
import { useApiLanguage } from '@/lib/i18n/utils';
import { RecipeNavigationButtons } from './recipe-navigation-buttons';
import { useI18n } from '@/hooks/use-i18n';

/**
 * Causes Selection component
 */
export function CausesSelection() {
  const apiLanguage = useApiLanguage();
  const { t } = useI18n();

  const {
    healthConcern,
    demographics,
    selectedCauses,
    potentialCauses,
    updateSelectedCauses,
    setPotentialSymptoms,
    isLoading,
    error,
    setError,
    clearError,
    isStreamingCauses,
    streamingError
  } = useRecipeStore();

  const { goToNext, goToPrevious, canGoNext, canGoPrevious, markCurrentStepCompleted } = useRecipeWizardNavigation();
  const [selectedCauseIds, setSelectedCauseIds] = useState<Set<string>>(new Set());

  // AI Streaming for symptoms (triggered when user clicks Continue)
  const {
    startStream,
    partialData,
    isStreaming: isStreamingSymptoms,
    isComplete: isSymptomsComplete,
    finalData: symptomsFinalData,
    error: _streamError // Prefix with underscore to indicate it's intentionally unused
  } = useAIStreaming({
    jsonArrayPath: 'data.potential_symptoms'
  });

  const [streamingItems, setStreamingItems] = useState<any[]>([]);

  // Ref to track navigation to prevent infinite loops
  const hasNavigatedRef = useRef(false);

  // Determine if we're in a loading state (either local loading or streaming from demographics)
  const isLoadingCauses = isStreamingCauses || isLoading;

  /**
   * Initialize selected causes from store
   */
  useEffect(() => {
    // SIMPLIFIED: Only log essential info instead of full object details
    console.log('🔄 Initializing selected causes from store:', selectedCauses.length, 'causes');

    if (selectedCauses.length > 0) {
      // CRITICAL FIX: Use cause_id instead of cause_name for selection tracking
      const ids = new Set(selectedCauses.map(cause => cause.cause_id));
      setSelectedCauseIds(ids);
      console.log('✅ Initialized selected cause IDs:', Array.from(ids));
    }
  }, [selectedCauses]);

  /**
   * Check if potential causes are available (now loaded via AI streaming from demographics step)
   */
  const loadPotentialCauses = useCallback(async () => {
    // If data is missing, check if we should redirect to earlier steps
    if (!healthConcern || !demographics) {
      // Don't show error immediately - let navigation handle redirects
      // Only show error if user tries to stay on this step
      return;
    }

    // Potential causes should already be loaded via AI streaming from demographics step
    // If not available and not currently streaming, show message to go back and complete demographics
    if (potentialCauses.length === 0 && !isStreamingCauses) {
      setError(t('create-recipe:causesSelection.error.noPotentialCauses', 'Potential causes not found. Please go back to the demographics step to generate them.'));
      return;
    }

    clearError();
  }, [healthConcern, demographics, potentialCauses.length, setError, clearError, t]);

  /**
   * Check if we have required data and show appropriate state
   */
  const checkRequiredData = useCallback(() => {
    if (!healthConcern || !demographics) {
      // Don't set errors during reset/navigation - let the navigation system handle redirects
      return;
    } else {
      clearError();
      loadPotentialCauses();
    }
  }, [healthConcern, demographics, loadPotentialCauses, clearError, t]);

  useEffect(() => {
    const cleanup = checkRequiredData();
    return cleanup;
  }, [checkRequiredData]);

  /**
   * OPTIMIZED: Handle symptoms streaming data updates using centralized transformation utilities
   * Eliminates ~30 lines of redundant transformation logic
   */
  useEffect(() => {
    if (partialData && Array.isArray(partialData) && partialData.length > 0) {
      const transformedSymptoms = transformRecipeWizardData(partialData, 'symptoms');
      if (transformedSymptoms.length > 0) {
        setPotentialSymptoms(transformedSymptoms);

        // Transform for modal display
        const modalItems = partialData.map((symptom: any) => ({
          title: symptom.name_localized,
          subtitle: symptom.suggestion_localized || 'Symptom suggestion',
          description: symptom.explanation_localized,
          timestamp: new Date()
        }));
        setStreamingItems(modalItems);
      }
    }
  }, [partialData, setPotentialSymptoms]);

  /**
   * OPTIMIZED: Handle symptoms streaming completion using centralized utilities
   * Eliminates ~30 lines of redundant final data processing logic
   */
  useEffect(() => {
    if (isSymptomsComplete && symptomsFinalData && !hasNavigatedRef.current) {
      console.log('✅ Symptoms streaming completed, navigating to symptoms page');

      hasNavigatedRef.current = true;

      // Extract and transform final data using centralized utilities
      const extractedData = extractFinalStreamingData(symptomsFinalData, 'data.potential_symptoms');
      const finalTransformedSymptoms = transformRecipeWizardData(extractedData, 'symptoms');

      if (finalTransformedSymptoms.length > 0) {
        setPotentialSymptoms(finalTransformedSymptoms);
      }

      console.log('🔄 Synchronizing modal closing and navigation to symptoms page');
      
      // Navigate to symptoms page immediately in the same event loop cycle
      if (canGoNext()) {
        goToNext();
      }
    }
  }, [isSymptomsComplete, symptomsFinalData, canGoNext, goToNext, setPotentialSymptoms]);

  /**
   * Handle cause selection toggle
   */
  const handleCauseToggle = useCallback((cause: PotentialCause, e?: React.MouseEvent) => {
    e?.stopPropagation();
    
    const causeId = cause.cause_id;

    // Safety check: ensure cause has a valid ID
    if (!causeId) {
      console.error('❌ Cause missing ID:', cause);
      setError(t('validation.invalidCause', 'Invalid cause data. Please refresh and try again.'));
      return;
    }

    setSelectedCauseIds(prev => {
      const newSelected = new Set(prev);
      if (newSelected.has(causeId)) {
        newSelected.delete(causeId);
      } else {
        newSelected.add(causeId);
        clearError();
      }
      return newSelected;
    });

    // Update store with selected causes in the next tick
    setTimeout(() => {
      const newSelectedIds = new Set(selectedCauseIds);
      if (newSelectedIds.has(causeId)) {
        newSelectedIds.delete(causeId);
      } else {
        newSelectedIds.add(causeId);
      }
      
      const newSelectedCauses = potentialCauses.filter(c => 
        newSelectedIds.has(c.cause_id)
      );
      
      updateSelectedCauses(newSelectedCauses);
      
      if (newSelectedCauses.length > 0) {
        markCurrentStepCompleted();
      }
    }, 0);
  }, [potentialCauses, selectedCauseIds, updateSelectedCauses, markCurrentStepCompleted, t]);

  /**
   * Handle form submission - Start symptoms streaming (following demographics pattern)
   */
  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (selectedCauseIds.size === 0) {
      setError(t('create-recipe:causesSelection.error.noCauseSelected', 'Please select at least one potential cause.'));
      return;
    }

    if (isStreamingSymptoms) {
      console.log('⏳ Symptoms streaming already in progress');
      return;
    }

    try {
      // Mark step as completed
      markCurrentStepCompleted();

      // Clear any previous errors
      clearError();

      // Reset navigation flag
      hasNavigatedRef.current = false;

      // Start symptoms streaming (stay on current page like demographics does)
      console.log('🚀 Starting symptoms analysis from causes page...');

      // OPTIMIZED: Use createStreamRequest utility for consistent request building
      // This same pattern used by demographics step
      const requestData = createStreamRequest(
        'create-recipe',
        'potential-symptoms',
        healthConcern!,
        demographics!,
        selectedCauses,
        [], // selectedSymptoms (empty for this step)
        apiLanguage
      );

      // SIMPLIFIED: Only log essential info instead of full request data
      console.log('🚀 Starting symptoms streaming with', selectedCauses.length, 'selected causes');
      await startStream('/api/ai/streaming', requestData);

    } catch (error) {
      console.error('Failed to start symptoms streaming:', error);
      setError(t('create-recipe:causesSelection.error.failedToAnalyze', 'Failed to analyze symptoms. Please try again.'));
      hasNavigatedRef.current = false;
    }
  };

  /**
   * Handle go back
   */
  const handleGoBack = async () => {
    if (canGoPrevious()) {
      await goToPrevious();
    }
  };

  /**
   * Handle retry loading causes
   */
  const handleRetry = () => {
    clearError();
    loadPotentialCauses();
  };

  const isFormValid = selectedCauseIds.size > 0 && selectedCauseIds.size <= potentialCauses.length;

  // Memoize the causes data to prevent unnecessary re-renders
  const causesData = useMemo(() => {
    return potentialCauses.map(cause => ({
      ...cause,
      isSelected: selectedCauseIds.has(cause.cause_id),
    }));
  }, [potentialCauses, selectedCauseIds]);

  return (
    <div data-testid="causes-selection" className="space-y-6">
      {/* Header */}
      <div className="space-y-2">
        <h2 className="text-2xl font-bold text-foreground">
          {t('create-recipe:causesSelection.title')}
        </h2>
        <p className="text-muted-foreground">
          {t('create-recipe:causesSelection.description')}
        </p>
      </div>

      {/* Health Concern Summary */}
      {healthConcern && (
        <div className="bg-muted/50 rounded-lg p-4">
          <h3 className="text-sm font-medium text-foreground mb-2">{t('create-recipe:causesSelection.healthConcernLabel')}</h3>
          <p className="text-sm text-muted-foreground italic">
            "{healthConcern.healthConcern}"
          </p>
        </div>
      )}

      {/* Error Display */}
      {(error || streamingError) && (
        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-4">
          <div className="flex items-center justify-between">
            <p className="text-destructive text-sm">{error || streamingError}</p>
            {(error?.includes('Failed to load') || streamingError?.includes('failed')) && (
              <Button
                variant="destructive"
                size="sm"
                onClick={handleRetry}
                className="ml-4"
              >
                {t('create-recipe:causesSelection.error.retry', 'Retry')}
              </Button>
            )}
          </div>
        </div>
      )}

      {/* Loading State */}
      {isLoadingCauses && (
        <div className="flex items-center justify-center py-12">
          <div className="text-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-primary" />
            <p className="text-muted-foreground">
              {isStreamingCauses
                ? t('create-recipe:causesSelection.loading.aiAnalyzing', 'AI is analyzing your information to identify potential causes...')
                : t('create-recipe:causesSelection.loading.loadingCauses', 'Loading potential causes...')
              }
            </p>
            {isStreamingCauses && (
              <p className="text-xs text-muted-foreground">
                {t('create-recipe:causesSelection.loading.mayTakeMoment', 'This may take a few moments as we generate personalized recommendations')}
              </p>
            )}
          </div>
        </div>
      )}

      {/* Streaming Progress Indicator */}
      {isStreamingCauses && potentialCauses.length > 0 && (
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div className="flex items-center space-x-3">
            <div className="animate-pulse rounded-full h-3 w-3 bg-blue-600"></div>
            <div>
              <p className="text-sm font-medium text-blue-800">
                {t('create-recipe:causesSelection.streaming.generating', 'Generating potential causes... ({count} found so far)', { count: potentialCauses.length })}
              </p>
              <p className="text-xs text-blue-600">
                {t('create-recipe:causesSelection.streaming.reviewWhileAnalyzing', 'You can review the causes below while we continue analyzing')}
              </p>
            </div>
          </div>
        </div>
      )}

      {/* Causes Table */}
      {potentialCauses.length > 0 && !isLoading && (
        <form onSubmit={onSubmit} className="space-y-6">
          {/* Selection Counter */}
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              {t('create-recipe:causesSelection.selectionCounter.label', 'Select 1-{count} causes that might apply to you', { count: potentialCauses.length })}
            </p>
            <span className={cn(
              "text-sm font-medium",
              selectedCauseIds.size > potentialCauses.length ? "text-destructive" : "text-foreground"
            )}>
              {t('create-recipe:causesSelection.selectionCounter.selected', '{selected}/{total} selected', { selected: selectedCauseIds.size, total: potentialCauses.length })}
            </span>
          </div>

          {/* Enhanced Table Container */}
          <div className="rounded-lg border border-border/50 bg-card/50 backdrop-blur-sm overflow-hidden shadow-sm">
            <Table>
              <TableHeader>
                <TableRow className="border-b border-border/50 bg-muted/30">
                  <TableHead className="w-16 h-14"></TableHead>
                  <TableHead className="font-semibold text-foreground">{t('create-recipe:causesSelection.tableHeader', 'Potential Causes')}</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {causesData.map((cause) => {
                  const isSelected = selectedCauseIds.has(cause.cause_id);
                  
                  return (
                    <TableRow 
                      key={cause.cause_id}
                      className={cn(
                        'group relative cursor-pointer transition-all duration-200 border-b border-border/30 last:border-b-0',
                        'hover:bg-accent/50 hover:shadow-sm',
                        isSelected ? 'bg-primary/5 border-primary/20' : 'hover:border-border/60'
                      )}
                      onClick={(e) => handleCauseToggle(cause, e)}
                    >
                      <TableCell className="w-16 py-4">
                        <div 
                          className="flex items-center justify-center"
                          onClick={(e) => e.stopPropagation()}
                        >
                          <div className={cn(
                            'flex items-center justify-center h-5 w-5 rounded-md border-2 transition-all duration-200',
                            'shadow-sm hover:shadow-md',
                            isSelected 
                              ? 'bg-primary border-primary shadow-primary/20' 
                              : 'border-input bg-background group-hover:border-primary/60 group-hover:bg-primary/5'
                          )}>
                            {isSelected && (
                              <svg 
                                className="h-3.5 w-3.5 text-primary-foreground" 
                                viewBox="0 0 14 14" 
                                fill="none"
                              >
                                <path 
                                  d="M11.6668 3.5L5.25016 9.91667L2.3335 7" 
                                  stroke="currentColor" 
                                  strokeWidth="2" 
                                  strokeLinecap="round" 
                                  strokeLinejoin="round"
                                />
                              </svg>
                            )}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell className="py-4 pr-6">
                        <div className="space-y-2">
                          <div className="font-medium text-foreground group-hover:text-primary transition-colors duration-200">
                            {cause.cause_name}
                          </div>
                          {cause.cause_suggestion && (
                            <div className="text-sm text-muted-foreground leading-relaxed">
                              {cause.cause_suggestion}
                            </div>
                          )}
                          {cause.explanation && (
                            <div className="text-sm text-muted-foreground/80 leading-relaxed italic">
                              {cause.explanation}
                            </div>
                          )}
                        </div>
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </div>

          {/* Action Buttons */}
          <RecipeNavigationButtons
            onPrevious={handleGoBack}
            onNext={() => onSubmit({ preventDefault: () => {} } as React.FormEvent)}
            canGoPrevious={canGoPrevious()}
            canGoNext={canGoNext()}
            isValid={isFormValid}
            isLoading={isLoading}
            previousLabel={t('common:buttons.previous', 'Previous')}
            nextLabel={t('common:buttons.continue', 'Continue')}
            statusMessage={t('create-recipe:causesSelection.status.ready', 'Ready to continue')}
            statusType="success"
          />
        </form>
      )}

      {/* Empty State */}
      {!isLoadingCauses && potentialCauses.length === 0 && !error && !streamingError && (
        <div className="text-center py-12">
          <p className="text-muted-foreground">
            {t('create-recipe:causesSelection.emptyState.noCauses', 'No potential causes found. Please go back and check your health concern.')}
          </p>
          <Button
            onClick={handleGoBack}
            variant="secondary"
            className="mt-4"
          >
            ← {t('common:buttons.goBack', 'Go Back')}
          </Button>
        </div>
      )}

      {/* AI Streaming Modal for Symptoms */}
      <AIStreamingModal
        isOpen={isStreamingSymptoms}
        title={t('streaming.modal.symptoms.title', 'AI Analysis in Progress')}
        description={t('streaming.modal.symptoms.description', 'Analyzing your selected causes to identify potential symptoms')}
        items={streamingItems}
        onClose={() => {
          // Optional: Allow users to minimize modal but keep streaming
          console.log('User requested to close symptoms streaming modal');
        }}
        maxVisibleItems={100}
        analysisType="symptoms"
      />
    </div>
  );
}
