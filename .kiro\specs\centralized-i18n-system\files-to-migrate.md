# Centralized i18n System - Files Migration List

## Overview

This document provides a comprehensive list of all files that need to be updated to use the centralized i18n system. Files are categorized by priority and migration complexity.

## Migration Priority Classification

### 🔴 High Priority - User-Facing Components
Components that directly impact user experience and SEO

### 🟡 Medium Priority - Supporting Components  
Components that support user-facing features but are not directly visible

### 🟢 Low Priority - Internal/Development
Internal utilities, tests, and development tools

## Files Requiring Migration

### 🔴 High Priority Files

#### Create Recipe Feature Components
1. **`src/features/create-recipe/components/mobile-layout.tsx`**
   - **Current Pattern**: Client component using `useTranslations` from `@/lib/i18n`
   - **Migration Pattern**: Client component with server-provided translations
   - **Namespace**: `create-recipe`
   - **Translation Keys**: Navigation, progress, step labels
   - **Complexity**: Medium (requires parent component updates)

2. **`src/features/create-recipe/components/auth-guard.tsx`**
   - **Current Pattern**: Client component using `useTranslations` from `@/lib/i18n`
   - **Migration Pattern**: Client component with server-provided translations
   - **Namespace**: `auth`
   - **Translation Keys**: Loading messages, unauthorized messages, profile incomplete messages
   - **Complexity**: Medium (multiple fallback components)

3. **`src/features/create-recipe/components/error-boundary.tsx`**
   - **Current Pattern**: Client component using `useTranslations` from `@/lib/i18n`
   - **Migration Pattern**: Client component with server-provided translations
   - **Namespace**: `common`
   - **Translation Keys**: Error messages, retry buttons
   - **Complexity**: Low (simple error display)

4. **`src/features/create-recipe/components/loading-skeletons.tsx`**
   - **Current Pattern**: Client component using `useTranslations` from `@/lib/i18n`
   - **Migration Pattern**: Client component with server-provided translations
   - **Namespace**: `common`
   - **Translation Keys**: Loading messages
   - **Complexity**: Low (simple loading states)

### 🟡 Medium Priority Files

#### Hook Files
7. **`src/hooks/use-i18n.ts`**
   - **Current Pattern**: Client-side i18n hook with `useEffect` and `useState`
   - **Migration Pattern**: Update to work with server-provided translations or deprecate
   - **Namespace**: N/A (utility)
   - **Translation Keys**: N/A
   - **Complexity**: Medium (hook refactoring)

8. **`src/hooks/use-server-i18n.ts`**
   - **Current Pattern**: Server-side i18n hook for Pages Router
   - **Migration Pattern**: Update for App Router compatibility
   - **Namespace**: N/A (utility)
   - **Translation Keys**: N/A
   - **Complexity**: Medium (router compatibility)

#### Utility Files
9. **`src/lib/i18n/utils/server-language-utils.ts`**
   - **Current Pattern**: Contains duplicate `parseAcceptLanguage` function
   - **Migration Pattern**: Remove duplicate, use centralized utility
   - **Namespace**: N/A (utility)
   - **Translation Keys**: N/A
   - **Complexity**: Low (remove duplicate code)

### 🟢 Low Priority Files

#### SEO Components
10. **`src/components/seo/LocalizedHead.tsx`** (if exists)
    - **Current Pattern**: Custom SEO component for Pages Router
    - **Migration Pattern**: Replace with App Router `generateMetadata`
    - **Namespace**: N/A (SEO utility)
    - **Translation Keys**: N/A
    - **Complexity**: Low (replace with built-in functionality)

#### Legacy i18n Files
11. **`src/lib/i18n/server.ts`** (cleanup)
    - **Current Pattern**: Contains duplicate `parseAcceptLanguage` function
    - **Migration Pattern**: Remove duplicate, keep main functionality
    - **Namespace**: N/A (utility)
    - **Translation Keys**: N/A
    - **Complexity**: Low (code cleanup)

12. **`src/lib/i18n/user-preference-redirect.ts`** (cleanup)
    - **Current Pattern**: Contains duplicate `parseAcceptLanguage` function
    - **Migration Pattern**: Remove duplicate, use centralized utility
    - **Namespace**: N/A (utility)
    - **Translation Keys**: N/A
    - **Complexity**: Low (code cleanup)

## Files Already Migrated ✅

### Reference Implementations
- **`middleware.ts`** ✅ - Centralized locale detection and redirects
- **`src/app/page.tsx`** ✅ - Simplified fallback page
- **`src/lib/i18n/request.ts`** ✅ - Moved to proper location
- **`src/features/homepage/layout/homepage-layout-i18n.tsx`** ✅ - Reference implementation

## Migration Dependencies

### Component Hierarchy
```
pages/create-recipe.tsx (High Priority)
├── src/features/create-recipe/components/mobile-layout.tsx (High Priority)
│   ├── BreadcrumbNavigation (May need migration)
│   └── CompactBreadcrumbNavigation (May need migration)
├── src/features/create-recipe/components/auth-guard.tsx (High Priority)
│   ├── AuthLoadingFallback (Included)
│   ├── UnauthorizedFallback (Included)
│   └── ProfileIncompleteFallback (Included)
├── src/features/create-recipe/components/error-boundary.tsx (High Priority)
└── src/features/create-recipe/components/loading-skeletons.tsx (High Priority)
```

### Migration Order Recommendation
1. **Phase 1**: ✅ Translation files already exist and are comprehensive
2. **Phase 2**: Migrate utility files and remove duplicates
3. **Phase 3**: Migrate leaf components (loading-skeletons, error-boundary)
4. **Phase 4**: Migrate complex components (mobile-layout, auth-guard)
5. **Phase 5**: ~~Migrate page components~~ (App Router - no Pages Router components)
6. **Phase 6**: ~~Migrate app structure~~ (App Router - no _app.tsx needed)

## Translation Namespaces Status

### ✅ Existing Translation Files (All Complete)
All required translation files already exist with comprehensive translations:

```
src/lib/i18n/messages/en/
├── create-recipe.json ✅ (Comprehensive recipe creation flow)
├── auth.json ✅ (Authentication flows)
├── common.json ✅ (Shared UI elements)
├── homepage.json ✅ (Homepage content)
└── dashboard.json ✅ (Dashboard features)

src/lib/i18n/messages/pt/
├── create-recipe.json ✅ (Portuguese translations)
├── auth.json ✅ (Portuguese translations)
├── common.json ✅ (Portuguese translations)
├── homepage.json ✅ (Portuguese translations)
└── dashboard.json ✅ (Portuguese translations)

src/lib/i18n/messages/es/
├── create-recipe.json ✅ (Spanish translations)
├── auth.json ✅ (Spanish translations)
├── common.json ✅ (Spanish translations)
├── homepage.json ✅ (Spanish translations)
└── dashboard.json ✅ (Spanish translations)
```

**Status**: All translation files are complete with AI translation instructions and proper context annotations.

### Translation Key Mapping (Based on Existing Files)

The existing translation files follow a comprehensive structure with AI translation instructions and proper context annotations. Here are the key patterns used in components:

#### create-recipe.json (Existing Structure)
Key sections available for component migration:
```json
{
  "_ai_translation_instructions": {
    "target_language": "English (US)",
    "context": "Multi-step essential oil recipe creation wizard...",
    "tone": "Supportive, encouraging, and professional..."
  },
  "title": "Essential Oil Recipe Creator",
  "subtitle": "Get personalized essential oil recommendations...",
  "navigation": {
    "breadcrumb": {
      "ariaLabel": "Recipe creation progress",
      "progress": "Progress",
      "completed": "{completedCount} of {totalSteps} completed"
    },
    "progress": "Step {current} of {total}",
    "buttons": {
      "previous": "Previous",
      "next": "Next",
      "complete": "Complete"
    }
  },
  "wizard": {
    "status": {
      "loading": "Loading..."
    },
    "buttons": {
      "retry": "Retry"
    }
  },
  "validation": {
    "healthConcern": "Please describe your health concern",
    "demographics": "Please complete your demographic information"
  }
}
```

#### auth.json (Existing Structure)
Key sections for auth components:
```json
{
  "_ai_translation_instructions": {
    "target_language": "English (US)",
    "context": "Authentication flows including login, registration...",
    "tone": "Welcoming, helpful, and reassuring..."
  },
  "login": {
    "title": "Welcome Back",
    "subtitle": "Sign in to your account"
  },
  "register": {
    "title": "Create Account",
    "subtitle": "Join us today"
  },
  "forgotPassword": {
    "title": "Reset Password",
    "subtitle": "Enter your email to receive reset instructions"
  }
}
```

#### common.json (Existing Structure)
Key sections for shared UI elements:
```json
{
  "_ai_translation_instructions": {
    "target_language": "English (US)",
    "context": "Common UI elements used throughout the application...",
    "tone": "Clear, professional, and accessible..."
  },
  "buttons": {
    "save": "Save",
    "cancel": "Cancel",
    "continue": "Continue",
    "back": "Back",
    "previous": "Previous",
    "next": "Next",
    "retry": "Retry",
    "processing": "Processing...",
    "analyzing": "Analyzing..."
  },
  "messages": {
    "readyToContinue": "✓ Ready to continue"
  },
  "status": {
    "saving": "Saving...",
    "ready": "✓ Ready",
    "lastSaved": "Last saved: {time}"
  }
}
```

**Key Patterns Observed:**
- All files include `_ai_translation_instructions` with context and tone
- Hierarchical structure with nested objects for organization
- Variable interpolation using `{variable}` syntax
- Context annotations using `_context` keys for AI guidance
- Consistent naming with snake_case for keys
- Comprehensive coverage of all UI states and messages

## Search Commands for Finding Additional Files

### Find Files Using Old i18n Patterns
```bash
# Find files importing from old i18n locations
grep -r "from ['\"]@/lib/i18n['\"]" src/ --include="*.ts" --include="*.tsx"

# Find files using useTranslations hook
grep -r "useTranslations" src/ --include="*.ts" --include="*.tsx"

# Find files using client-side locale detection
grep -r "useEffect.*locale" src/ --include="*.ts" --include="*.tsx"
grep -r "useState.*locale" src/ --include="*.ts" --include="*.tsx"

# Find files using getI18nServerSideProps
grep -r "getI18nServerSideProps" src/ pages/ --include="*.ts" --include="*.tsx"

# Find files using LocalizedHead component
grep -r "LocalizedHead" src/ pages/ --include="*.ts" --include="*.tsx"

# Find files with parseAcceptLanguage duplicates
grep -r "parseAcceptLanguage" src/ --include="*.ts" --include="*.tsx"
```

### Find Translation Usage Patterns
```bash
# Find translation key usage patterns
grep -r "t(['\"]" src/ --include="*.ts" --include="*.tsx"

# Find namespace usage patterns
grep -r ":[a-zA-Z_]" src/ --include="*.ts" --include="*.tsx"

# Find variable interpolation patterns
grep -r "{{.*}}" src/ --include="*.json"
```

## Validation Checklist

### Pre-Migration Validation
- [ ] All files identified and categorized
- [ ] Dependencies mapped correctly
- [ ] Translation namespaces planned
- [ ] Migration order established

### During Migration Validation
- [ ] Each file migrated according to its pattern
- [ ] Translation keys created for all locales
- [ ] Parent components updated for client components
- [ ] Imports updated correctly

### Post-Migration Validation
- [ ] All components render without errors
- [ ] Translations display correctly in all locales
- [ ] SEO metadata generated properly
- [ ] No client-side hydration issues
- [ ] Performance impact assessed

## Estimated Migration Effort

### Time Estimates (per file)
- **High Priority Components**: 2-4 hours each
- **Medium Priority Components**: 1-2 hours each  
- **Low Priority Components**: 0.5-1 hour each

### Total Estimated Effort
- **High Priority**: 6 files × 3 hours = 18 hours
- **Medium Priority**: 2 files × 1.5 hours = 3 hours
- **Low Priority**: 4 files × 0.75 hours = 3 hours
- **Translation Files**: 3 namespaces × 3 locales × 0.5 hours = 4.5 hours
- **Testing & Validation**: 4 hours

**Total Estimated Effort**: ~32.5 hours

### Risk Factors
- **Pages Router to App Router conversion**: High complexity
- **Component hierarchy dependencies**: Medium complexity
- **Translation key mapping accuracy**: Medium complexity
- **SEO metadata preservation**: Low complexity

## Success Criteria

### Functional Requirements
- [ ] All user-facing text is translatable
- [ ] All supported locales work correctly
- [ ] Fallback behavior works for missing translations
- [ ] Variable interpolation works correctly

### Performance Requirements
- [ ] No client-side hydration issues
- [ ] Server-side rendering works correctly
- [ ] Translation loading is cached properly
- [ ] No unnecessary re-renders

### SEO Requirements
- [ ] Metadata generated in correct language
- [ ] Hreflang links present and correct
- [ ] Content fully translated on initial load
- [ ] No flash of untranslated content (FOUC)

### Maintainability Requirements
- [ ] Code follows established patterns
- [ ] TypeScript types are correct
- [ ] No duplicate code remains
- [ ] Documentation is updated

This comprehensive migration list ensures that all components using the old i18n system are identified and can be systematically migrated to the centralized i18n system while maintaining functionality and improving performance.