# Centralized i18n System Design

## Overview

This design creates a centralized, reusable i18n system following the project's established architecture patterns. The system will be structured under `/src/lib/i18n/` to provide consistent internationalization capabilities across all features (homepage, create-recipe, dashboard, etc.) while maintaining DRY and KISS principles. The current implementation in `/app/page.tsx` violates DRY principles and lacks reusability across features.

## Architecture

### Current Problems
- Redirect logic is duplicated between middleware and `/app/page.tsx`
- Locale detection happens in multiple places with different implementations
- Authentication-based locale preference is only handled in page component
- Non-DRY approach makes maintenance difficult
- No reusable i18n services for other features (create-recipe, dashboard, etc.)
- Current implementation doesn't follow established `/src/lib/` architecture patterns

### Proposed Solution
Reorganize existing i18n logic following established architecture patterns:
1. Move existing redirect logic from `/app/page.tsx` to middleware for performance and DRY compliance
2. Relocate existing locale detection functions to `/src/lib/i18n/` structure
3. Enhance existing middleware to handle authentication-based locale preferences
4. Maintain backward compatibility while preparing for legacy cleanup

## Components and Interfaces

### Enhanced Middleware
The middleware will be enhanced to handle all locale detection and redirects:

```typescript
// middleware.ts - Enhanced version
export function middleware(request: NextRequest) {
  // 1. Skip excluded paths (existing)
  // 2. Handle URLs with existing locales (existing)
  // 3. NEW: Handle root URL redirects with full locale detection
  // 4. NEW: Integrate authentication-based locale preferences
}
```

### Existing i18n Infrastructure
The project already has comprehensive i18n infrastructure that needs to be integrated:

```typescript
// src/lib/i18n/server.ts - EXISTING
// Complete server-only translation system with user preference integration
export const getLocaleWithUserPreference = cache(async (urlLocale?: string): Promise<SupportedLocale>)
export const createTranslatorWithUserPreference = cache(async (namespace: string, urlLocale?: string))
```

```typescript
// src/lib/i18n/user-preference-redirect.ts - EXISTING  
// User preference-based redirect utilities
export async function redirectToUserPreferredLocale(requestedPath: string, fallbackLocale: SupportedLocale)
export async function getUserPreferredLocale(): Promise<SupportedLocale>
```

```typescript
// src/lib/i18n/types/i18n.ts - EXISTING
// Complete type definitions for i18n system
export type SupportedLocale = 'en' | 'pt' | 'es'
```

### Enhanced Middleware Integration
Integrate existing auth services into middleware for complete locale detection:

```typescript
// middleware.ts - Enhanced to handle root URL redirects
// Move logic from /app/page.tsx to middleware using existing services
export async function detectLocaleInMiddleware(request: NextRequest): Promise<SupportedLocale>
```

### Existing i18n Request Configuration
Utilize existing `/src/i18n/request.ts` for server-side translations:

```typescript
// src/i18n/request.ts - Already exists
// Uses next-intl for server-side translation handling
```

### Authentication Integration
Integrate with existing auth services in middleware:

```typescript
// Use existing services in middleware context
import { getServerAuthState } from '@/features/auth/services/auth-state.service'
import { getCurrentUserProfile } from '@/features/user-auth-data/services/profile.service'
```

### Authentication Integration
Integrate with existing auth services in middleware:

```typescript
// Use existing services in middleware context
import { getServerAuthState } from '@/features/auth/services/auth-state.service'
import { getCurrentUserProfile } from '@/features/user-auth-data/services/profile.service'
```

## Data Models

### SupportedLocale Type
```typescript
type SupportedLocale = 'en' | 'pt' | 'es';
```

### Locale Detection Priority
1. **Authenticated users**: Database language preference
2. **Non-authenticated users**: Browser Accept-Language header
3. **Fallback**: English ('en')

## Error Handling

### Middleware Error Handling
- Graceful fallback to default locale ('en') on any error
- Development logging for debugging
- No user-facing errors during locale detection

### Service Error Handling
- Database connection failures fall back to browser detection
- Invalid locale values fall back to default
- Malformed Accept-Language headers fall back to default

## Testing Strategy

### Unit Tests
- Test locale detection service with various Accept-Language headers
- Test authentication integration with different user states
- Test error handling scenarios

### Integration Tests
- Test complete redirect flow for authenticated users
- Test complete redirect flow for non-authenticated users
- Test middleware behavior with various URL patterns

### Manual Testing
- Verify existing functionality remains unchanged
- Test with different browser language settings
- Test with authenticated users having different language preferences

## Implementation Approach

### Phase 1: Create Locale Detection Service
- Extract and centralize locale detection logic
- Create reusable service functions
- Maintain existing behavior exactly

### Phase 2: Enhance Middleware
- Move redirect logic from page component to middleware
- Integrate authentication services in middleware
- Handle all redirect scenarios in one place

### Phase 3: Simplify Page Component
- Remove redirect logic from `/app/page.tsx`
- Create simple fallback page (should rarely be reached)
- Clean up duplicate code

### Phase 4: Legacy Documentation
- Document all files/functions that become legacy
- Prepare for future cleanup of client-side detection
- Create migration path for remaining client-side logic

## Key Design Decisions

### Why Middleware Over Page Component
- **Performance**: Redirects happen at edge/server level, not after page load
- **DRY**: Single place for all redirect logic
- **Consistency**: Same logic applies to all routes
- **SEO**: Better for search engines (301 redirects vs client redirects)

### Authentication in Middleware
- Use existing auth services to maintain consistency
- Handle async operations properly in middleware context
- Graceful fallback when auth services fail

### Backward Compatibility
- Maintain exact same user experience
- Keep all existing functionality working
- No breaking changes to existing features

## File Structure

```
src/
├── lib/
│   └── i18n/
│       ├── server.ts                           # EXISTING - Complete server translation system
│       ├── user-preference-redirect.ts         # EXISTING - User preference redirect utilities
│       └── types/
│           └── i18n.ts                         # EXISTING - Complete type definitions
├── i18n/
│   └── request.ts                              # EXISTING - Already configured
├── app/
│   └── page.tsx                                # SIMPLIFIED - Remove redirect logic
└── middleware.ts                               # ENHANCED - Handle all redirects
```

This design maintains KISS principles by keeping the solution simple and focused, while following DRY by centralizing all locale detection and redirect logic in one place. The approach **reuses existing comprehensive i18n infrastructure** rather than creating new files, ensuring minimal disruption to the current system.