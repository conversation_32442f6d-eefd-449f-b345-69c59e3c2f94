#!/usr/bin/env node
/**
 * Simple verification script for i18n locale detection fix
 * Run with: node scripts/verify-i18n-fix.js
 */

// Test the parseAcceptLanguage function logic
function parseAcceptLanguage(acceptLanguage) {
  const supportedLocales = ['en', 'pt', 'es'];

  if (!acceptLanguage) return 'en';

  try {
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const [langCode, weight] = lang.trim().split(';');
        const code = langCode?.substring(0, 2).toLowerCase() || '';
        const priority = weight ? parseFloat(weight.split('=')[1]) : 1.0;
        return { code, priority };
      })
      .filter(lang => lang.code) // Filter out empty codes
      .sort((a, b) => b.priority - a.priority);

    // Find the first supported language
    for (const lang of languages) {
      if (supportedLocales.includes(lang.code)) {
        return lang.code;
      }
    }
  } catch (error) {
    // If parsing fails, fall back to default
  }

  return 'en';
}

// Test scenarios
const testCases = [
  {
    name: 'Portuguese Browser (pt-BR)',
    input: 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
    expected: 'pt'
  },
  {
    name: 'Spanish Browser (es-ES)',
    input: 'es-ES,es;q=0.9,en;q=0.8',
    expected: 'es'
  },
  {
    name: 'English Browser (en-US)',
    input: 'en-US,en;q=0.9',
    expected: 'en'
  },
  {
    name: 'French Browser (unsupported)',
    input: 'fr-FR,fr;q=0.9,de;q=0.8',
    expected: 'en'
  },
  {
    name: 'Malformed header',
    input: 'invalid-header-format',
    expected: 'en'
  },
  {
    name: 'Empty header',
    input: '',
    expected: 'en'
  }
];

console.log('🧪 i18n Browser Language Detection Verification');
console.log('===============================================\n');

let passed = 0;
let total = testCases.length;

testCases.forEach((testCase, index) => {
  const result = parseAcceptLanguage(testCase.input);
  const success = result === testCase.expected;
  
  console.log(`${index + 1}. ${testCase.name}`);
  console.log(`   Input: "${testCase.input}"`);
  console.log(`   Expected: ${testCase.expected}`);
  console.log(`   Got: ${result}`);
  console.log(`   Result: ${success ? '✅ PASS' : '❌ FAIL'}\n`);
  
  if (success) passed++;
});

console.log(`Summary: ${passed}/${total} tests passed (${Math.round((passed/total) * 100)}%)`);

if (passed === total) {
  console.log('🎉 All browser language detection tests passed!');
  console.log('\n✅ The fix should work correctly for:');
  console.log('   - Portuguese browsers (pt-BR → pt)');
  console.log('   - Spanish browsers (es-ES → es)');
  console.log('   - English browsers (en-US → en)');
  console.log('   - Unsupported languages (fallback to en)');
  console.log('   - Malformed headers (fallback to en)');
} else {
  console.log('❌ Some tests failed. The implementation needs review.');
}

console.log('\n📋 Next Steps:');
console.log('1. Test the actual application with different browser languages');
console.log('2. Verify authenticated user preferences still work');
console.log('3. Check that URL locale takes priority over user preferences');
console.log('4. Ensure SEO optimization is maintained');
