/**
 * @fileoverview Protocol summary card component with flip animation
 * Shows overview of each recipe protocol with flip interaction
 */

'use client';

import React, { useState } from 'react';
import { RecipeTimeSlot, FinalRecipeProtocol } from '../types/recipe.types';
import { useI18n } from '@/hooks/use-i18n';
import { getTimeSlotConfig } from '../constants/time-slot-config';

interface ProtocolSummaryCardProps {
  timeSlot: RecipeTimeSlot;
  recipe: FinalRecipeProtocol | null;
  onViewDetails: () => void;
}

/**
 * Protocol summary card with flip animation
 * Follows the exact design from standalone-v1.html flip cards
 */
export const ProtocolSummaryCard = React.memo(function ProtocolSummaryCard({ timeSlot, recipe, onViewDetails }: ProtocolSummaryCardProps) {
  const [isFlipped, setIsFlipped] = useState(false);
  const { t } = useI18n();

  const config = getTimeSlotConfig(timeSlot);
  
  // Get localized labels
  const localizedConfig = {
    ...config,
    label: t(`create-recipe:steps.final-recipes.protocols.${timeSlot === 'mid-day' ? 'midDay' : timeSlot}.subtitle`),
    purpose: t(`create-recipe:steps.final-recipes.protocols.${timeSlot === 'mid-day' ? 'midDay' : timeSlot}.purpose`)
  };

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const handleViewRecipe = () => {
    onViewDetails();
  };

  if (!recipe) {
    return (
      <div className="h-96 bg-muted rounded-2xl animate-pulse flex items-center justify-center border border-border">
        <div className="text-muted-foreground">{t('create-recipe:steps.final-recipes.loading')}</div>
      </div>
    );
  }

  return (
    <div className="h-96 perspective-1000">
      <div className={`
        relative w-full h-full transition-transform duration-800 transform-style-preserve-3d
        ${isFlipped ? 'rotate-y-180' : ''}
      `}>
        {/* Front of card */}
        <div className={`
          absolute w-full h-full backface-hidden rounded-2xl overflow-hidden
          bg-gradient-to-br ${config.gradient} p-8 flex flex-col items-center justify-between
          text-white shadow-2xl ${config.shadowColor}
        `}>
          <div className="w-full flex flex-col items-start">
            <span className={`text-sm uppercase tracking-widest ${config.accentColor}`}>
              PROTOCOLO
            </span>
            <div className="flex items-center gap-2 mt-2">
              <span className="text-2xl">{config.emoji}</span>
              <h3 className="text-2xl font-black">{config.label}</h3>
            </div>
          </div>
          
          <div className="flex-1 flex flex-col items-center justify-center w-full mt-4">
            <div className="text-center">
              <div className={`text-base ${config.accentColor}`}>{t('create-recipe:steps.final-recipes.overview.protocolSummary.synergyFor')}</div>
              <div className="text-xl font-bold mb-6">{localizedConfig.purpose}</div>
            </div>
            <button
              onClick={handleFlip}
              className={`
                inline-flex items-center gap-2 px-6 py-2.5 font-semibold rounded-full
                shadow-lg transition-all duration-200 ${config.buttonColor}
              `}
            >
              {t('create-recipe:steps.final-recipes.overview.protocolSummary.viewDetails')}
              <svg xmlns='http://www.w3.org/2000/svg' className='h-5 w-5' viewBox='0 0 20 20' fill='currentColor'>
                <path fillRule='evenodd' d='M12.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-2.293-2.293a1 1 0 010-1.414z' clipRule='evenodd'/>
              </svg>
            </button>
          </div>
          
          <div className={`text-center text-xs ${config.accentColor} mt-4 w-full`}>
            {t('create-recipe:steps.final-recipes.overview.protocolSummary.clickToView')}
          </div>
        </div>

        {/* Back of card */}
        <div className="absolute w-full h-full backface-hidden rotate-y-180 bg-card p-6 overflow-y-auto rounded-2xl border border-border shadow-lg">
          <button
            onClick={handleFlip}
            className="absolute top-4 right-4 text-muted-foreground hover:text-primary text-2xl leading-none rounded-full focus:outline-none focus:ring-2 focus:ring-primary"
            aria-label={t('create-recipe:steps.final-recipes.overview.protocolSummary.close')}
          >
            &times;
          </button>
          
          <div className="mt-2">
            <div className="mb-2 text-sm text-card-foreground">
              <strong>{t('create-recipe:steps.final-recipes.overview.protocolSummary.objective')}:</strong> {recipe.description_localized}
            </div>
          </div>
          
          <div className="mt-4">
            <ul className="mt-2 space-y-1">
              {recipe.selected_oils.map((oil, index) => (
                <li key={index} className="flex justify-between items-center">
                  <span className="text-primary">● {oil.name_localized}</span>
                  <span className="font-mono text-sm text-muted-foreground">{oil.drops_count} {t('create-recipe:steps.final-recipes.overview.protocolSummary.drops')}</span>
                </li>
              ))}
            </ul>
          </div>
          
          <div className="mt-4">
            <div className="mb-2 text-sm text-card-foreground">
              <strong>{t('create-recipe:steps.final-recipes.overview.protocolSummary.quickPrep')}:</strong> {t('create-recipe:steps.final-recipes.overview.protocolSummary.prepInstructions', { 
                size: recipe.container_recommendation.size_ml,
                carrierOil: recipe.carrier_oil.name_localized
              })}
            </div>
            <div className="mb-2 text-sm text-card-foreground">
              <strong>{t('create-recipe:steps.final-recipes.overview.protocolSummary.howToUse')}:</strong> {recipe.usage_instructions_localized[0] || recipe.application_method_localized}
            </div>
          </div>
          
          <div className="mt-2 text-right">
            <button
              onClick={handleViewRecipe}
              className="text-sm text-primary hover:text-primary/80 hover:underline transition-colors"
            >
              {t('create-recipe:steps.final-recipes.overview.protocolSummary.viewRecipe')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
});
