/**
 * @fileoverview Properties Display component for Essential Oil Recipe Creator.
 * Displays therapeutic properties already loaded by Symptoms Selection step and handles oil suggestions.
 * OPTIMIZED: Removed duplicate properties streaming - properties are loaded in Symptoms Selection step.
 */

'use client';

import React, { useEffect, useCallback, useRef, useState } from 'react';
import { useRecipeStore } from '../store/recipe-store';
import { useRecipeWizardNavigation } from '../hooks/use-recipe-navigation';
import { useCreateRecipeStreaming } from '../hooks/use-create-recipe-streaming';
import { useI18n } from '@/hooks/use-i18n';
import { useApiLanguage } from '@/lib/i18n/utils';
import { TherapeuticPropertiesTable } from './therapeutic-properties-table';
import {
  TherapeuticProperty,
  EnrichedEssentialOil,
  EssentialOil
} from '../types/recipe.types';
// ok_to_future_delete - Import debug overlay component
import { RecipeDebugOverlay } from './recipe-debug-overlay';

import { Button } from '@/components/ui/button';
import { RecipeNavigationButtons } from './recipe-navigation-buttons';

// Add the triggerBatchEnrichment function
async function triggerBatchEnrichment(suggestedOils: EssentialOil[]) {
  const response = await fetch('/api/ai/batch-enrichment', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({ suggestedOils })
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  const data = await response.json();
  if (!data.enriched_oils) {
    throw new Error('No enriched oils in response');
  }

  return data;
}

// Add a helper to log the full state of therapeuticProperties
function logTherapeuticPropertiesState(label: string, properties: TherapeuticProperty[]) {
  console.log(`🟦 [DEBUG] ${label} - therapeuticProperties:`);
  properties.forEach((p, idx) => {
    console.log(`  [${idx}] ${p.property_id} | ${p.property_name_localized} | isEnriched: ${p.isEnriched} | oils: ${p.suggested_oils?.length || 0}`);
    if (p.suggested_oils) {
      p.suggested_oils.forEach((oil, oidx) => {
        console.log(`    [oil ${oidx}] ${oil.oil_id} | ${oil.name_english} | status: ${oil.enrichment_status}`);
      });
    }
  });
}

/**
 * Properties Display component - displays already-loaded therapeutic properties with oil suggestion capabilities
 */
export function PropertiesDisplay() {
  const {
    healthConcern,
    demographics,
    selectedCauses,
    selectedSymptoms,
    therapeuticProperties,
    updateTherapeuticProperties,
    error,
    setError,
    clearError,
    shouldAutoAnalyzeProperties,
    setShouldAutoAnalyzeProperties,
    propertyEnrichmentStatus,
    setPropertyEnrichmentStatus,
    updatePropertyWithEnrichedOils
  } = useRecipeStore();

  // Remove processedPropertiesRef initialization

  // Selectors for functions to prevent unnecessary re-renders
  // These are now directly available from the main hook above
  // const updatePropertyWithEnrichedOils = useRecipeStore(state => state.updatePropertyWithEnrichedOils);
  // const setPropertyEnrichmentStatus = useRecipeStore(state => state.setPropertyEnrichmentStatus);

  const { goToNext, goToPrevious, canGoNext, canGoPrevious, markCurrentStepCompleted } = useRecipeWizardNavigation();
  const apiLanguage = useApiLanguage();
  const { t } = useI18n();

  // Get parallel streaming functionality
  const { 
    streamingState: parallelStreamingState, 
    startOilSuggestionStreaming,
    resetState: resetParallelStreams 
  } = useCreateRecipeStreaming();

  /**
   * Check if required data is available (properties should be loaded by Symptoms Selection step)
   */
  const checkRequiredData = useCallback(() => {
    if (!healthConcern || !demographics || selectedCauses.length === 0 || selectedSymptoms.length === 0) {
      return; // Let navigation handle redirects
    }
    
    if (therapeuticProperties.length === 0) {
      setError('Therapeutic properties not found. Please go back to the symptoms step.');
      return;
    }
    
    clearError();
    
    // Auto-mark step as completed when properties are available
    if (therapeuticProperties.length > 0) {
      markCurrentStepCompleted();
    }
  }, [healthConcern, demographics, selectedCauses, selectedSymptoms, therapeuticProperties.length, clearError, setError, markCurrentStepCompleted]);

  useEffect(() => {
    checkRequiredData();
  }, [checkRequiredData]);

  /**
   * Handle "Analyze All Properties" button click
   */
  const handleAnalyzeAllProperties = useCallback(async () => {
    if (!healthConcern || !demographics || therapeuticProperties.length === 0) {
      console.warn('Missing required data for oil suggestions');
      return;
    }

    console.log('ACTION: handleAnalyzeAllProperties');
    console.log('PAYLOAD:', { healthConcern, demographics, therapeuticProperties });
    console.log('🚀 Starting parallel oil analysis for all properties');
    
    // SIMPLIFIED: Only log essential info instead of full property details
    console.log('📤 SENDING - Properties for analysis:', therapeuticProperties.length, 'properties');

    try {
      // Reset previous state
      resetParallelStreams();
      
      // Start parallel streaming with all required parameters
      await startOilSuggestionStreaming(
        therapeuticProperties,
        healthConcern,
        demographics,
        selectedCauses,
        selectedSymptoms,
        apiLanguage
      );

      console.log('📊 Parallel streaming started. Updating UI to loading state.');
      
      // Update UI to show loading state for all properties
      const loadingProperties = therapeuticProperties.map(p => ({
        ...p,
        isLoadingOils: true,
        errorLoadingOils: null,
      }));

      updateTherapeuticProperties(loadingProperties, 'parallelStreaming (Start)');
      logTherapeuticPropertiesState('After batch loading', loadingProperties);
      
    } catch (error) {
      console.error('❌ Failed to start parallel analysis:', error);
      setError('Failed to analyze properties. Please try again.');
    }
  }, [healthConcern, demographics, therapeuticProperties, resetParallelStreams, startOilSuggestionStreaming, selectedCauses, selectedSymptoms, apiLanguage, updateTherapeuticProperties, setError]);

  // Ref to prevent double auto-trigger execution
  const autoTriggerExecutedRef = React.useRef(false);

  /**
   * Auto-trigger oil analysis when navigating to properties page
   * 
   * This effect implements the automatic oil analysis functionality that eliminates
   * the need for users to manually click "Analyze All Properties". The auto-trigger
   * only occurs when:
   * 
   * 1. shouldAutoAnalyzeProperties flag is true (set by symptoms component)
   * 2. Properties exist and some lack oil suggestions
   * 3. Required data (healthConcern, demographics) is available
   * 4. No streaming operation is currently active
   * 5. Auto-trigger hasn't already been executed (prevents double execution)
   * 
   * The flag is cleared immediately after triggering to prevent duplicate API calls.
   * A 800ms delay is added for better UX - allows users to see properties before analysis begins.
   * 
   * @see setShouldAutoAnalyzeProperties in symptoms-selection.tsx
   */
  useEffect(() => {
    // CRITICAL: Check execution guard FIRST, before any other logic
    if (autoTriggerExecutedRef.current) {
      console.log('❌ Auto-trigger skipped: already executed (early check)');
      return;
    }
    
    console.log('🔍 AUTO-TRIGGER DEBUG:', {
      shouldAutoAnalyzeProperties,
      autoTriggerExecuted: autoTriggerExecutedRef.current,
      propertiesCount: therapeuticProperties.length,
      hasHealthConcern: !!healthConcern,
      hasDemographics: !!demographics,
      isStreaming: parallelStreamingState.isStreaming
    });
    
    // Only proceed if auto-analysis is requested
    if (!shouldAutoAnalyzeProperties) {
      console.log('❌ Auto-trigger skipped: shouldAutoAnalyzeProperties is false');
      return;
    }
    
    const hasPropertiesWithoutOils = therapeuticProperties.some(p => !p.suggested_oils?.length);
    const hasRequiredData = healthConcern && demographics && therapeuticProperties.length > 0;
    const canAnalyze = hasPropertiesWithoutOils && hasRequiredData && !parallelStreamingState.isStreaming;
    
    console.log('🔍 AUTO-TRIGGER CONDITIONS:', {
      hasPropertiesWithoutOils,
      hasRequiredData,
      canAnalyze,
      propertiesWithoutOils: therapeuticProperties.filter(p => !p.suggested_oils?.length).length
    });
    
    if (canAnalyze) {
      console.log('🤖 Auto-triggering oil analysis based on navigation intent');
      
      // CRITICAL: Mark as executed IMMEDIATELY to prevent any race conditions
      autoTriggerExecutedRef.current = true;
      
      // Clear the flag to prevent re-triggering
      setShouldAutoAnalyzeProperties(false);
      
      // Execute immediately without timeout to avoid cleanup issues
      // The 800ms delay was causing the timeout to be cancelled by re-renders
      console.log('🚀 About to call handleAnalyzeAllProperties...');
      console.log('🔍 Function details:', {
        functionExists: typeof handleAnalyzeAllProperties === 'function',
        functionName: handleAnalyzeAllProperties.name,
        requiredDataCheck: {
          healthConcern: !!healthConcern,
          demographics: !!demographics,
          propertiesLength: therapeuticProperties.length
        }
      });
      
      (async () => {
        try {
          console.log('🚀 Calling handleAnalyzeAllProperties now...');
          const result = await handleAnalyzeAllProperties();
          console.log('✅ handleAnalyzeAllProperties returned:', result);
          console.log('✅ Auto-trigger completed successfully');
        } catch (error) {
          console.error('❌ Auto-trigger failed:', error);
          console.error('❌ Error details:', error);
          // Reset execution flag on error to allow retry
          autoTriggerExecutedRef.current = false;
        }
      })();
    } else {
      console.log('❌ Auto-trigger conditions not met');
    }
    
    // Return undefined for cases where no cleanup is needed
    return undefined;
  }, [therapeuticProperties, shouldAutoAnalyzeProperties, healthConcern, demographics, parallelStreamingState.isStreaming, setShouldAutoAnalyzeProperties]);

  // Reset auto-trigger ref only when we get a fresh shouldAutoAnalyzeProperties=true from a new navigation
  // We use a more specific condition to avoid resetting during re-renders
  const prevShouldAutoAnalyzeRef = React.useRef(shouldAutoAnalyzeProperties);
  useEffect(() => {
    // Only reset if shouldAutoAnalyzeProperties changed from false to true (new navigation)
    if (shouldAutoAnalyzeProperties && !prevShouldAutoAnalyzeRef.current) {
      console.log('🔄 Resetting auto-trigger ref for new navigation');
      autoTriggerExecutedRef.current = false;
    }
    prevShouldAutoAnalyzeRef.current = shouldAutoAnalyzeProperties;
  }, [shouldAutoAnalyzeProperties]);

  /**
   * Handle parallel streaming state updates - only when streaming starts
   */
  const isStreamingRef = useRef(parallelStreamingState.isStreaming);
  useEffect(() => {
    // Only trigger when streaming starts (false -> true transition)
    if (parallelStreamingState.isStreaming && !isStreamingRef.current) {
      console.log('📊 Parallel streaming started. Updating UI to loading state.');
      const currentProperties = useRecipeStore.getState().therapeuticProperties;
      updateTherapeuticProperties(
        currentProperties.map(p => ({
          ...p,
          isLoadingOils: !p.suggested_oils?.length,
          errorLoadingOils: null
        })),
        'parallelStreaming (Start)'
      );
    }
    isStreamingRef.current = parallelStreamingState.isStreaming;
  }, [parallelStreamingState.isStreaming]);

  // SOLUTION: Fix stale closure by using fresh state and proper dependencies
  const handleStreamingResults = useCallback(() => {
    if (parallelStreamingState.results.size === 0) return;

    console.log('ACTION: handleStreamingResults');
    console.log('PAYLOAD:', parallelStreamingState.results);

    // By including therapeuticProperties in the dependency array below,
    // this function always has the latest state and avoids overwriting
    // completed enrichments with stale data.
    therapeuticProperties.forEach(async (p: TherapeuticProperty) => {
      // Skip if already processed this result
      if (propertyEnrichmentStatus[p.property_id] === 'loading' || propertyEnrichmentStatus[p.property_id] === 'success') {
        return;
      }

      // Handle streaming results for each property
      if (parallelStreamingState.results.has(p.property_id)) {
        const result = parallelStreamingState.results.get(p.property_id);
        const suggested_oils = result?.suggested_oils.map((oil: EssentialOil) => ({
          ...oil,
          isEnriched: oil.isEnriched ?? false
        })) || [];

        // Only trigger enrichment if we have oils to enrich and property hasn't been enriched yet
        if (suggested_oils.length > 0 && !p.isEnriched) {
          console.log(`ACTION: triggerBatchEnrichment for property: ${p.property_id}`);
          console.log('PAYLOAD:', suggested_oils);
          console.log(`🔄 [properties-display] Triggering enrichment for property ${p.property_id} with ${suggested_oils.length} oils`);
          
          setPropertyEnrichmentStatus(p.property_id, 'loading');
          
          try {
            const data = await triggerBatchEnrichment(suggested_oils);
            
            await updatePropertyWithEnrichedOils(p.property_id, data.enriched_oils);
            logTherapeuticPropertiesState(`After enrichment for property ${p.property_id}`, useRecipeStore.getState().therapeuticProperties);
            
            const allOilsProcessed = data.enriched_oils.every((oil: EnrichedEssentialOil) => 
              oil.enrichment_status === 'enriched' || 
              oil.enrichment_status === 'not_found' || 
              oil.enrichment_status === 'discarded'
            );
            setPropertyEnrichmentStatus(p.property_id, allOilsProcessed ? 'success' : 'error');
            
            console.log(`✅ [properties-display] Enrichment completed for property ${p.property_id}. All oils processed: ${allOilsProcessed}`);
          } catch (error) {
            console.error(`❌ [properties-display] Enrichment failed for property ${p.property_id}:`, error);
            setPropertyEnrichmentStatus(p.property_id, 'error');
            logTherapeuticPropertiesState(`After error for property ${p.property_id}`, useRecipeStore.getState().therapeuticProperties);
          }
        }
      }
    });
  }, [
    parallelStreamingState.results, 
    therapeuticProperties, 
    propertyEnrichmentStatus, 
    updatePropertyWithEnrichedOils, 
    setPropertyEnrichmentStatus
  ]);

  // Remove processedPropertiesRef cleanup effect

  /**
   * Handle parallel streaming results
   */
  useEffect(() => {
    handleStreamingResults();
  }, [handleStreamingResults]);

  /**
   * Handle parallel streaming errors
   */
  useEffect(() => {
    if (parallelStreamingState.errors.size === 0) return;

    const currentProperties = useRecipeStore.getState().therapeuticProperties;

    // Fix: Remove unused 'errorMsg' variable
    parallelStreamingState.errors.forEach((_, propertyId) => {
      // Only update the error/loading state for the affected property
      const property = currentProperties.find(p => p.property_id === propertyId);
      if (property) {
        updatePropertyWithEnrichedOils(propertyId, property.suggested_oils || []);
        setPropertyEnrichmentStatus(propertyId, 'error');
        logTherapeuticPropertiesState(`After error for property ${propertyId}`, useRecipeStore.getState().therapeuticProperties);
      }
    });
  }, [parallelStreamingState.errors, updatePropertyWithEnrichedOils, setPropertyEnrichmentStatus]);

  /**
   * Monitor property-level progress (only meaningful progress we can track)
   * Each property agent decides dynamically how many tools to call, so tool progress is meaningless
   */
  const propertyProgressCount = React.useMemo(() => {
    if (!parallelStreamingState.isStreaming) return { completed: 0, total: 0 };
    
    // Count properties that have completed (either with results or errors)
    const completedCount = parallelStreamingState.results.size + parallelStreamingState.errors.size;
    const totalProperties = therapeuticProperties.length;
    
    return {
      completed: completedCount,
      total: totalProperties
    };
  }, [parallelStreamingState.isStreaming, parallelStreamingState.results.size, parallelStreamingState.errors.size, therapeuticProperties.length]);

  useEffect(() => {
    if (parallelStreamingState.isStreaming) {
      console.log(`📊 Property Analysis Progress: ${propertyProgressCount.completed}/${propertyProgressCount.total} properties completed`);
      console.log(`📊 Tool Calls Made: ${parallelStreamingState.completedCount} total | Results: ${parallelStreamingState.results.size} | Errors: ${parallelStreamingState.errors.size}`);
    }
  }, [parallelStreamingState.isStreaming, propertyProgressCount, parallelStreamingState.completedCount, parallelStreamingState.results.size, parallelStreamingState.errors.size]);

  // ok_to_future_delete - Debug overlay state
  const [showDebugOverlay, setShowDebugOverlay] = useState(false);

  /**
   * Navigation handlers
   */
  const handleContinue = async () => {
    try {
      console.log('🚀 [Properties Navigation] Continue button clicked');

      // Log current state for debugging
      const canNavigate = canGoNext();
      console.log('🔍 [Properties Navigation] Navigation validation:', {
        canGoNext: canNavigate,
        therapeuticPropertiesCount: therapeuticProperties.length,
        enrichedPropertiesCount: therapeuticProperties.filter(p => p.isEnriched).length,
        allPropertiesEnriched: therapeuticProperties.every(p => p.isEnriched),
        hasHealthConcern: !!healthConcern,
        hasDemographics: !!demographics,
        selectedCausesCount: selectedCauses.length,
        selectedSymptomsCount: selectedSymptoms.length
      });

      // Log detailed enrichment status
      therapeuticProperties.forEach((prop, index) => {
        console.log(`🔍 [Properties Navigation] Property ${index + 1}: ${prop.property_name_localized}`, {
          isEnriched: prop.isEnriched,
          oilsCount: prop.suggested_oils?.length || 0,
          enrichedOilsCount: prop.suggested_oils?.filter(oil => oil.enrichment_status).length || 0
        });
      });

      markCurrentStepCompleted();

      // REMOVED: No longer populating suggestedOils array
      // Final Recipes now reads directly from therapeuticProperties (same as debug overlay)
      console.log('🍃 [Properties Navigation] Properties step completed - Final Recipes will read from therapeuticProperties');

      // Attempt to navigate to next step
      if (canNavigate) {
        console.log('✅ [Properties Navigation] Validation passed, navigating to next step');
        await goToNext();
      } else {
        console.warn('❌ [Properties Navigation] Cannot navigate - validation failed');
        setError('Cannot proceed to next step. Please ensure all properties are enriched.');
      }

    } catch (error) {
      console.error('❌ [Properties Navigation] Navigation failed:', error);
      setError('Failed to proceed to next step. Please try again.');
    }
  };

  // ok_to_future_delete - Handle debug overlay close (navigation removed)
  const handleDebugOverlayClose = async () => {
    console.log('🔧 [Debug] Closing debug overlay (no navigation)');
    setShowDebugOverlay(false);
  };

  const handleGoBack = async () => {
    if (canGoPrevious()) {
      await goToPrevious();
    }
  };

  // Error state
  if (error) {
    return (
      <div className="flex flex-col items-center justify-center gap-4 p-8">
        <p className="text-destructive text-sm">{t('create-recipe:error.' + error, error)}</p>
        <Button onClick={handleGoBack}>
          {t('common:buttons.goBack')}
        </Button>
      </div>
    );
  }

  // Check for missing properties
  if (therapeuticProperties.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center gap-4 p-8">
        <p>{t('create-recipe:steps.properties.noProperties')}</p>
        <Button onClick={handleGoBack}>
          {t('common:buttons.goBackSymptoms')}
        </Button>
      </div>
    );
  }

  // Main content
  return (
    <div className="flex flex-col gap-6">
      <div className="flex flex-col gap-2">
        <h2 className="text-2xl font-bold tracking-tight">{t('create-recipe:steps.properties.title')}</h2>
        <p className="text-muted-foreground">
          {parallelStreamingState.isStreaming ? (
            <>{t('create-recipe:steps.properties.analyzing', undefined, {
              count: propertyProgressCount.completed.toString(),
              total: propertyProgressCount.total.toString()
            })}</>
          ) : (
            <>
              {therapeuticProperties.some(p => p.suggested_oils?.length) ? (
                t('create-recipe:steps.properties.recommendationsReady')
              ) : (
                t('create-recipe:steps.properties.recommendationsLoading')
              )}
            </>
          )}
        </p>
      </div>

      {/* Properties Table */}
      <TherapeuticPropertiesTable
        properties={therapeuticProperties}
        selectedCauses={selectedCauses}
        selectedSymptoms={selectedSymptoms}
        parallelStreamingState={parallelStreamingState}
        propertyEnrichmentStatus={propertyEnrichmentStatus}
        setPropertyEnrichmentStatus={setPropertyEnrichmentStatus}
        updatePropertyWithEnrichedOils={updatePropertyWithEnrichedOils}
      />

      {/* Debug Overlay */}
      {process.env.NODE_ENV === 'development' && (
        <Button
          variant="outline"
          size="sm"
          className="mt-4"
          onClick={() => setShowDebugOverlay(true)}
        >
          {t('create-recipe:debug.showOverlay')}
        </Button>
      )}

      {/* Navigation */}
      <RecipeNavigationButtons
        canGoPrevious={canGoPrevious()}
        canGoNext={canGoNext()}
        onPrevious={handleGoBack}
        onNext={handleContinue}
        isValid={true}
        isLoading={false}
        previousLabel={t('common:buttons.previous')}
        nextLabel={t('create-recipe:steps.properties.next')}
        statusMessage={t('create-recipe:steps.properties.analysisComplete')}
        statusType="success"
      />

      {/* Debug Overlay Modal */}
      {showDebugOverlay && (
        <RecipeDebugOverlay
          isOpen={showDebugOverlay}
          onClose={handleDebugOverlayClose}
          properties={therapeuticProperties}
          streamingState={parallelStreamingState}
        />
      )}
    </div>
  );
}