#!/usr/bin/env tsx
/**
 * Standalone test script for i18n locale detection flow
 * Run with: npx tsx scripts/test-i18n-flow.ts
 * 
 * This script simulates different browser/user scenarios to test the i18n system
 */

import { createServer } from 'http';
import { parse } from 'url';

// Color codes for console output
const colors = {
  reset: '\x1b[0m',
  bright: '\x1b[1m',
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m'
};

// Test scenarios
const testScenarios = [
  {
    name: 'Portuguese Browser (pt-BR)',
    acceptLanguage: 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7',
    expectedLocale: 'pt',
    userAuth: false
  },
  {
    name: 'Spanish Browser (es-ES)',
    acceptLanguage: 'es-ES,es;q=0.9,en;q=0.8',
    expectedLocale: 'es',
    userAuth: false
  },
  {
    name: 'English Browser (en-US)',
    acceptLanguage: 'en-US,en;q=0.9',
    expectedLocale: 'en',
    userAuth: false
  },
  {
    name: 'French Browser (unsupported)',
    acceptLanguage: 'fr-FR,fr;q=0.9,de;q=0.8',
    expectedLocale: 'en', // Should fallback to English
    userAuth: false
  },
  {
    name: 'Authenticated User (Spanish preference, Portuguese browser)',
    acceptLanguage: 'pt-BR,pt;q=0.9,en;q=0.8',
    expectedLocale: 'es', // User preference should override browser
    userAuth: true,
    userLanguage: 'es'
  },
  {
    name: 'Authenticated User (No preference, Portuguese browser)',
    acceptLanguage: 'pt-BR,pt;q=0.9,en;q=0.8',
    expectedLocale: 'pt', // Should fallback to browser
    userAuth: true,
    userLanguage: null
  },
  {
    name: 'Malformed Accept-Language',
    acceptLanguage: 'invalid-header-format',
    expectedLocale: 'en',
    userAuth: false
  },
  {
    name: 'No Accept-Language Header',
    acceptLanguage: '',
    expectedLocale: 'en',
    userAuth: false
  }
];

/**
 * Parse Accept-Language header (duplicates the logic from the actual implementation)
 */
function parseAcceptLanguage(acceptLanguage: string): string {
  const supportedLocales = ['en', 'pt', 'es'];

  if (!acceptLanguage) return 'en';

  try {
    const languages = acceptLanguage
      .split(',')
      .map(lang => {
        const [langCode, weight] = lang.trim().split(';');
        const code = langCode?.substring(0, 2).toLowerCase() || '';
        const priority = weight ? parseFloat(weight.split('=')[1]) : 1.0;
        return { code, priority };
      })
      .filter(lang => lang.code) // Filter out empty codes
      .sort((a, b) => b.priority - a.priority);

    // Find the first supported language
    for (const lang of languages) {
      if (supportedLocales.includes(lang.code)) {
        return lang.code;
      }
    }
  } catch (error) {
    // If parsing fails, fall back to default
  }

  return 'en';
}

/**
 * Simulate user preference detection
 */
function getUserPreferredLocale(scenario: any): string {
  // 1. If user is authenticated and has a language preference
  if (scenario.userAuth && scenario.userLanguage) {
    return scenario.userLanguage;
  }

  // 2. If user is authenticated but has no preference, or not authenticated
  // Use browser detection
  return parseAcceptLanguage(scenario.acceptLanguage);
}

/**
 * Run a single test scenario
 */
function runTestScenario(scenario: any): boolean {
  const detectedLocale = getUserPreferredLocale(scenario);
  const passed = detectedLocale === scenario.expectedLocale;

  console.log(`\n${colors.bright}Test: ${scenario.name}${colors.reset}`);
  console.log(`  Accept-Language: ${colors.cyan}${scenario.acceptLanguage || '(none)'}${colors.reset}`);
  console.log(`  User Auth: ${colors.yellow}${scenario.userAuth}${colors.reset}`);
  if (scenario.userAuth) {
    console.log(`  User Language: ${colors.yellow}${scenario.userLanguage || '(none)'}${colors.reset}`);
  }
  console.log(`  Expected: ${colors.blue}${scenario.expectedLocale}${colors.reset}`);
  console.log(`  Detected: ${colors.blue}${detectedLocale}${colors.reset}`);
  console.log(`  Result: ${passed ? colors.green + '✓ PASS' : colors.red + '✗ FAIL'}${colors.reset}`);

  return passed;
}

/**
 * Run all test scenarios
 */
function runAllTests(): void {
  console.log(`${colors.bright}${colors.magenta}i18n Locale Detection Test Suite${colors.reset}`);
  console.log(`${colors.bright}=================================${colors.reset}\n`);

  let passed = 0;
  let total = testScenarios.length;

  for (const scenario of testScenarios) {
    if (runTestScenario(scenario)) {
      passed++;
    }
  }

  console.log(`\n${colors.bright}Summary:${colors.reset}`);
  console.log(`  Total Tests: ${total}`);
  console.log(`  Passed: ${colors.green}${passed}${colors.reset}`);
  console.log(`  Failed: ${passed === total ? colors.green + '0' : colors.red + (total - passed)}${colors.reset}`);
  console.log(`  Success Rate: ${colors.bright}${Math.round((passed / total) * 100)}%${colors.reset}`);

  if (passed === total) {
    console.log(`\n${colors.green}${colors.bright}🎉 All tests passed!${colors.reset}`);
  } else {
    console.log(`\n${colors.red}${colors.bright}❌ Some tests failed. Check the implementation.${colors.reset}`);
  }
}

/**
 * Interactive test mode
 */
function runInteractiveTest(): void {
  console.log(`${colors.bright}${colors.cyan}Interactive i18n Test Mode${colors.reset}`);
  console.log(`${colors.bright}===========================${colors.reset}\n`);

  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  function askForInput(): void {
    rl.question(`\nEnter Accept-Language header (or 'quit' to exit): ${colors.cyan}`, (acceptLanguage: string) => {
      if (acceptLanguage.toLowerCase() === 'quit') {
        rl.close();
        return;
      }

      rl.question(`Is user authenticated? (y/n): ${colors.yellow}`, (authAnswer: string) => {
        const isAuth = authAnswer.toLowerCase() === 'y';
        
        if (isAuth) {
          rl.question(`User language preference (en/pt/es or empty): ${colors.yellow}`, (userLang: string) => {
            const scenario = {
              acceptLanguage,
              userAuth: true,
              userLanguage: userLang || null
            };
            
            const detectedLocale = getUserPreferredLocale(scenario);
            console.log(`\n${colors.bright}Result:${colors.reset}`);
            console.log(`  Detected Locale: ${colors.green}${detectedLocale}${colors.reset}`);
            console.log(`  Redirect URL: ${colors.blue}/${detectedLocale}${colors.reset}`);
            
            askForInput();
          });
        } else {
          const scenario = {
            acceptLanguage,
            userAuth: false,
            userLanguage: null
          };
          
          const detectedLocale = getUserPreferredLocale(scenario);
          console.log(`\n${colors.bright}Result:${colors.reset}`);
          console.log(`  Detected Locale: ${colors.green}${detectedLocale}${colors.reset}`);
          console.log(`  Redirect URL: ${colors.blue}/${detectedLocale}${colors.reset}`);
          
          askForInput();
        }
      });
    });
  }

  askForInput();
}

/**
 * Main function
 */
function main(): void {
  const args = process.argv.slice(2);
  
  if (args.includes('--interactive') || args.includes('-i')) {
    runInteractiveTest();
  } else {
    runAllTests();
    
    console.log(`\n${colors.bright}Usage:${colors.reset}`);
    console.log(`  npx tsx scripts/test-i18n-flow.ts           # Run all test scenarios`);
    console.log(`  npx tsx scripts/test-i18n-flow.ts -i        # Interactive mode`);
    console.log(`  npx tsx scripts/test-i18n-flow.ts --help    # Show this help`);
  }
}

// Handle help flag
if (process.argv.includes('--help') || process.argv.includes('-h')) {
  console.log(`${colors.bright}i18n Locale Detection Test Script${colors.reset}`);
  console.log(`${colors.bright}==================================${colors.reset}\n`);
  console.log(`This script tests the i18n locale detection logic with various scenarios.\n`);
  console.log(`${colors.bright}Usage:${colors.reset}`);
  console.log(`  npx tsx scripts/test-i18n-flow.ts           # Run all test scenarios`);
  console.log(`  npx tsx scripts/test-i18n-flow.ts -i        # Interactive mode`);
  console.log(`  npx tsx scripts/test-i18n-flow.ts --help    # Show this help\n`);
  console.log(`${colors.bright}Test Scenarios:${colors.reset}`);
  testScenarios.forEach((scenario, index) => {
    console.log(`  ${index + 1}. ${scenario.name}`);
  });
  process.exit(0);
}

// Run the main function
main();
