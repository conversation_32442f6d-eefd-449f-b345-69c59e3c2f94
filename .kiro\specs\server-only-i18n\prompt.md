I need you to review the implementation of i18n (internationalization) functionality for the homepage feature located in `src/features/homepage/`. 

**Study these existing i18n implementation documents first:**
- `docs/homepage-i18n-integration.md`
- `docs/server-only-i18n-user-preferences.md` 
- `.kiro/specs/server-only-i18n/i18n-migration-log.md`
- docs\i18n-browser-detection-fix.md

Tests:
- src\lib\i18n\__tests__\locale-detection.test.ts
- scripts\test-i18n-flow.ts
- scripts\verify-i18n-fix.js

A[User visits URL] --> B{URL has locale?}
    
    %% Direct locale access
    B -->|Yes: /en, /pt, /es| C[Middleware: Extract locale from URL]
    C --> D[Set x-locale header]
    D --> E[Pass to localized page]
    E --> F[Page renders with URL locale]
    
    %% Root URL access
    B -->|No: / or /some-path| G[Middleware: Detect browser locale]
    G --> H[Set x-detected-locale header]
    H --> I[Pass to root page]
    I --> J[Root page: redirectToUserPreferredLocale]
    
    %% User preference detection
    J --> K{User authenticated?}
    
    %% Authenticated user flow
    K -->|Yes| L[Get user profile from DB]
    L --> M{User has language preference?}
    M -->|Yes: pt, en, es| N[Use user DB preference]
    M -->|No/Invalid| O[Fallback to browser detection]
    
    %% Non-authenticated user flow
    K -->|No| O[Use browser Accept-Language]
    
    %% Browser detection logic
    O --> P[Parse Accept-Language header]
    P --> Q{Supported locale found?}
    Q -->|Yes: pt-BR → pt| R[Use detected locale]
    Q -->|No| S[Use default: en]
    
    %% Final redirect
    N --> T[Redirect to /{locale}/]
    R --> T
    S --> T
    
    %% Final rendering
    T --> U[Localized page loads]
    U --> V[getOptimalLocale checks URL vs user preference]
    V --> W[Render with appropriate locale]
    
    %% Error handling
    J -.->|Error| X[Fallback to /en]
    L -.->|DB Error| O
    P -.->|Parse Error| S
    
    %% Styling
    classDef middleware fill:#e1f5fe
    classDef server fill:#f3e5f5
    classDef redirect fill:#fff3e0
    classDef final fill:#e8f5e8
    
    class C,D,G,H middleware
    class J,K,L,M,V server
    class T,X redirect
    class F,W final

**Requirements:**
1. Analyze the current homepage feature structure in `src/features/homepage/`
2. Study the existing SSR (Server-Side Rendering) i18n implementation patterns from the documentation
3. Identify the current middleware configuration and determine the proper execution sequence for i18n middleware integration
4. Implement the i18n functionality following the established patterns from the documentation
5. Ensure the implementation follows our i18n architecture preference: prioritize URL locale for SEO, then fall back to user's saved database language preference for authenticated users, with graceful handling of mismatches

**Specific tasks:**
- Review the middleware chain and determine where i18n middleware should be positioned
- Apply the SSR i18n patterns to the homepage components
- Ensure proper locale handling and user preference integration
- Test that the implementation works correctly with the existing middleware stack

**Context:** Based on our previous implementations, this should follow the server-only i18n approach with proper URL locale handling and user preference fallbacks as documented in the reference files.

Investigate and fix the browser language detection issue for non-authenticated users in the i18n system (it is detecting correctly, but not redirecting correctly). The logs show that browser locale detection is working correctly (detecting 'pt' from 'pt-BR,pt;q=0.9,en-US;q=0.8,en;q=0.7') but the user is still being redirected to '/en' instead of '/pt'.

**Specific Issues to Address:**
1. **Root Cause Analysis**: The middleware correctly detects 'pt' from Accept-Language header, but the redirect logic in `redirectToUserPreferredLocale()` is not using this detected locale
2. **Integration Gap**: There's a disconnect between middleware locale detection and server-side redirect logic in `src/lib/i18n/user-preference-redirect.ts`
3. **Priority System Implementation**: Ensure the locale priority system follows this exact flow:
   - If user is authenticated → use user's database language preference
   - If user is NOT authenticated → use browser Accept-Language detection
   - If user is authenticated BUT user language is null/empty/invalid → fallback to browser Accept-Language
   - If all detection methods fail → fallback to English

**Deliverables:**
1. **Fix the browser language detection** by ensuring middleware-detected locale is properly passed to and used by the server-side redirect logic
2. **Create a comprehensive flow diagram** (using Mermaid) that visualizes the complete i18n locale detection and redirect process, including all decision points and fallback scenarios
3. **Create comprehensive test files**:
   - Jest test suite (`src/lib/i18n/__tests__/locale-detection.test.ts`) covering all locale detection scenarios
   - Standalone test script (`scripts/test-i18n-flow.ts`) that can be run with `npx tsx` to manually test different browser/user scenarios

**Technical Requirements:**
- Examine the integration between `middleware.ts`, `src/lib/i18n/server.ts`, and `src/lib/i18n/user-preference-redirect.ts`
- Ensure the `parseAcceptLanguage()` function results are properly utilized in the redirect flow
- Maintain backward compatibility with existing authenticated user preferences
- Preserve SEO optimization where URL locale takes highest priority for direct access

**Context:** The current implementation already has all the pieces (middleware detection, server-side utilities, user preferences) but they're not properly integrated for the non-authenticated user browser detection scenario.