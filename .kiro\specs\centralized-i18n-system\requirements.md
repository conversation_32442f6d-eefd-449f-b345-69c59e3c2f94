# Centralized i18n System Requirements

## Introduction

Create a centralized, reusable i18n system that follows the project's established architecture patterns and can be used across all features (homepage, create-recipe, dashboard, etc.) while maintaining DRY and KISS principles.

## Requirements

### Requirement 1: Follow Established Architecture

**User Story:** As a developer, I want the i18n system to follow the same patterns as other features, so that it's consistent and maintainable.

#### Acceptance Criteria

1. WHEN implementing i18n functionality THEN it SHALL follow the existing `/src/features/` structure
2. WHEN creating services THEN they SHALL be placed in `src\lib\i18n\services/`
3. WHEN creating hooks THEN they SHALL be placed in `src\lib\i18n\hooks/`
4. W<PERSON><PERSON> creating utilities THEN they SHALL be placed in `src\lib\i18n\utils`
5. W<PERSON><PERSON> creating types THEN they SHALL be placed in `src\lib\i18n\types\i18n.ts`

### Requirement 2: Centralized Source of Truth

**User Story:** As a developer, I want one place to manage locale detection and redirects, so that I don't duplicate logic across pages. Currently is not in a DRY concept place that can be reused. It is located inside /app/page 

#### Acceptance Criteria

1. WHEN a user visits any non-localized URL THEN the middleware SHALL handle all redirects
2. WHEN determining user locale THEN the system SHALL use a single service with clear priority order
3. WHEN multiple features need i18n THEN they SHALL use the same centralized service
4. WHEN locale changes THEN all features SHALL automatically reflect the change

### Requirement 3: Reusable Across Features

**User Story:** As a developer, I want to easily add i18n to any feature, so that I can internationalize the entire application consistently.

#### Acceptance Criteria

1. WHEN adding i18n to create-recipe feature THEN it SHALL use the same hooks/services as homepage
2. WHEN adding i18n to dashboard feature THEN it SHALL require minimal setup
3. WHEN creating new features THEN they SHALL have access to the same i18n utilities
4. WHEN using i18n in server components THEN it SHALL work consistently across all features

### Requirement 4: Maintain Current Functionality

**User Story:** As a user, I want the current i18n behavior to continue working, so that my experience is not disrupted. but the last task on this will be to create a new SPEC to cleanup the current i18n legacy behavior which uses client-side detection and not SSR. A file should be created logging all files and functions that will be legacy at the end of the implementation.

#### Acceptance Criteria

1. WHEN visiting `/` THEN I SHALL be redirected to my preferred locale (`/pt` for authenticated users)
2. WHEN visiting `/pt`, `/en`, `/es` directly THEN the page SHALL load in that locale
3. WHEN not authenticated THEN browser language detection SHALL work (pt-BR → `/pt`)
4. WHEN authenticated THEN database language preference SHALL take priority, if empty or null, english fallback
5. WHEN middleware sets headers THEN server components SHALL receive correct locale

### Requirement 5: Clean Integration

**User Story:** As a developer, I want the i18n system to integrate cleanly with existing code, so that refactoring is minimal.

#### Acceptance Criteria

1. WHEN integrating with existing features THEN minimal changes SHALL be required
2. WHEN using in server components THEN it SHALL work with existing patterns
3. WHEN using in client components THEN it SHALL work with existing hooks
4. WHEN adding to new pages THEN it SHALL require only importing the service/hook