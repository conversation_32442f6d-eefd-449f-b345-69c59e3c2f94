/**
 * @fileoverview Shared time slot configuration for Final Recipes components
 * Centralizes styling, labels, and behavior for morning, mid-day, and night protocols
 */

import { RecipeTimeSlot } from '../types/recipe.types';

export interface TimeSlotConfig {
  emoji: string;
  label: string;
  subtitle: string;
  timeRange: string;
  purpose: string;
  // Theme-based styling
  gradient: string;
  shadowColor: string;
  accentColor: string;
  buttonColor: string;
  badgeColor: string;
}

export const TIME_SLOT_CONFIG: Record<RecipeTimeSlot, TimeSlotConfig> = {
  morning: {
    emoji: '🌅',
    label: 'Morning',
    subtitle: 'Morning Protocol',
    timeRange: '7:00 AM - 9:00 AM',
    purpose: 'Focus & Calm',
    // Theme variables for consistent styling
    gradient: 'from-muted-foreground to-foreground',
    shadowColor: 'shadow-muted/30',
    accentColor: 'text-primary/80',
    buttonColor: 'bg-card text-card-foreground hover:bg-accent',
    badgeColor: 'bg-primary/10 text-primary'
  },
  'mid-day': {
    emoji: '☀️',
    label: 'Daytime',
    subtitle: 'Daytime Protocol',
    timeRange: '12:00 PM - 2:00 PM',
    purpose: 'Immediate Pain Relief',
    // Theme variables for consistent styling
    gradient: 'from-primary/80 to-primary',
    shadowColor: 'shadow-primary/30',
    accentColor: 'text-primary-foreground/80',
    buttonColor: 'bg-card text-card-foreground hover:bg-accent',
    badgeColor: 'bg-accent/10 text-accent-foreground'
  },
  night: {
    emoji: '🌙',
    label: 'Evening',
    subtitle: 'Evening Protocol',
    timeRange: '8:00 PM - 10:00 PM',
    purpose: 'Restorative Sleep',
    // Theme variables for consistent styling
    gradient: 'from-accent to-accent-foreground',
    shadowColor: 'shadow-accent/30',
    accentColor: 'text-accent-foreground/80',
    buttonColor: 'bg-card text-card-foreground hover:bg-accent',
    badgeColor: 'bg-secondary/10 text-secondary-foreground'
  }
};

/**
 * Get configuration for a specific time slot
 */
export function getTimeSlotConfig(timeSlot: RecipeTimeSlot): TimeSlotConfig {
  return TIME_SLOT_CONFIG[timeSlot];
}

/**
 * Get all time slots in order
 */
export function getTimeSlots(): RecipeTimeSlot[] {
  return ['morning', 'mid-day', 'night'];
}