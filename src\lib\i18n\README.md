# 🌍 Centralized i18n System - Single Source of Truth

**The definitive guide for internationalization in this Next.js application.**

## 🎯 System Overview

This is a **centralized, server-first i18n system** that handles all locale detection, redirects, and translations through middleware and server components. It's designed for optimal performance, SEO, and developer experience.

### Key Principles
- **Server-First**: All locale detection and redirects happen server-side
- **Middleware-Driven**: Single source of truth for routing and locale detection
- **Edge Runtime Compatible**: Works with Next.js Edge Runtime for global performance
- **DRY Compliant**: No duplicate logic across the application
- **Type-Safe**: Full TypeScript support with strict typing

## 🏗️ Architecture

```
Request Flow:
1. User visits any URL
2. Middleware detects locale (cookie → Accept-Language → default)
3. Middleware redirects to localized URL (/pt, /en, /es)
4. Server components receive locale via headers
5. Translations loaded server-side for optimal performance
```

### File Structure
```
middleware.ts                           # 🎯 SINGLE SOURCE OF TRUTH for redirects
src/lib/i18n/
├── server.ts                          # Server-side translation utilities
├── user-preference-redirect.ts        # User preference handling
├── request.ts                         # next-intl configuration
├── types/i18n.ts                     # TypeScript definitions
├── messages/                          # Translation files
│   ├── en/                           # English (default)
│   ├── pt/                           # Portuguese
│   └── es/                           # Spanish
└── README.md                          # This documentation
```

## 🚀 Quick Start

### 1. Server Components (Recommended)
```tsx
import { getOptimalLocale, createTranslatorWithUserPreference } from '@/lib/i18n/server';

export default async function MyPage() {
  const locale = await getOptimalLocale();
  const t = await createTranslatorWithUserPreference('my-feature');
  
  return (
    <div>
      <h1>{t('title')}</h1>
      <p>{t('description')}</p>
    </div>
  );
}
```

### 2. Page Metadata
```tsx
import { generateSEOMetadata } from '@/lib/i18n/server';

export async function generateMetadata() {
  return await generateSEOMetadata('my-feature', 'page');
}
```

### 3. Translation Files
```json
// src/lib/i18n/messages/en/my-feature.json
{
  "title": "My Feature",
  "description": "Feature description",
  "page_meta_title": "My Feature - App Name",
  "page_meta_description": "SEO description"
}
```

## 🔧 Core Functions

### Server-Side Functions (Primary)

#### `getOptimalLocale(urlLocale?: string)`
Gets the best locale for the current user with authentication integration.
```tsx
const locale = await getOptimalLocale(); // 'en' | 'pt' | 'es'
```

#### `createTranslatorWithUserPreference(namespace: string, urlLocale?: string)`
Creates a translation function with user preference integration.
```tsx
const t = await createTranslatorWithUserPreference('homepage');
const title = t('title'); // Translated string
```

#### `generateSEOMetadata(namespace: string, pageKey: string)`
Generates SEO metadata with translations and hreflang links.
```tsx
export async function generateMetadata() {
  return await generateSEOMetadata('homepage', 'main');
}
```

### Utility Functions

#### `getLocale()`
Gets locale from middleware headers.
```tsx
const locale = await getLocale(); // Current request locale
```

#### `getTranslations(namespace: string, locale?: SupportedLocale)`
Loads translation file for a namespace.
```tsx
const translations = await getTranslations('homepage', 'pt');
```

## 🌐 Locale Detection System

### Priority Order
1. **URL Locale** (`/pt/page`) - Highest priority for SEO
2. **User Database Preference** (authenticated users) - Personal preference
3. **Cookie Preference** (`locale=pt`) - Browser storage
4. **Accept-Language Header** (`pt-BR,pt;q=0.9`) - Browser setting
5. **Default Fallback** (`en`) - Safe fallback

### How It Works
```typescript
// Middleware handles all detection and redirects
// Example flow:
// 1. User visits "/"
// 2. Middleware checks: cookie=pt → redirects to "/pt"
// 3. User visits "/pt" → middleware sets headers
// 4. Server component gets locale from headers
```

## 📁 Translation File Organization

### Namespace Structure
```
src/lib/i18n/messages/
├── en/                    # English (base language)
│   ├── common.json        # Shared UI elements
│   ├── homepage.json      # Homepage content
│   ├── dashboard.json     # Dashboard features
│   └── create-recipe.json # Recipe creation
├── pt/                    # Portuguese
│   └── ... (same structure)
└── es/                    # Spanish
    └── ... (same structure)
```

### Translation File Format
```json
{
  "title": "Page Title",
  "description": "Page description",
  "buttons": {
    "save": "Save",
    "cancel": "Cancel"
  },
  "meta_title": "SEO Title",
  "meta_description": "SEO Description"
}
```

### Key Naming Conventions
- Use `snake_case` for translation keys
- Use `meta_` prefix for SEO metadata
- Group related keys in objects
- Keep keys descriptive and specific

## 🎯 Adding i18n to New Features

### Step 1: Create Translation Files
```bash
# Create translation files for all locales
touch src/lib/i18n/messages/en/my-feature.json
touch src/lib/i18n/messages/pt/my-feature.json
touch src/lib/i18n/messages/es/my-feature.json
```

### Step 2: Add Translations
```json
// src/lib/i18n/messages/en/my-feature.json
{
  "title": "My Feature",
  "subtitle": "Feature subtitle",
  "actions": {
    "create": "Create New",
    "edit": "Edit",
    "delete": "Delete"
  },
  "meta_title": "My Feature - App Name",
  "meta_description": "Feature description for SEO"
}
```

### Step 3: Use in Components
```tsx
import { createTranslatorWithUserPreference } from '@/lib/i18n/server';

export default async function MyFeature() {
  const t = await createTranslatorWithUserPreference('my-feature');
  
  return (
    <div>
      <h1>{t('title')}</h1>
      <p>{t('subtitle')}</p>
      <button>{t('actions.create')}</button>
    </div>
  );
}
```

### Step 4: Add Metadata
```tsx
import { generateSEOMetadata } from '@/lib/i18n/server';

export async function generateMetadata() {
  return await generateSEOMetadata('my-feature', 'main');
}
```

## 🔄 User Language Preferences

### How User Preferences Work
1. **Authenticated Users**: Language preference stored in database
2. **Cookie Fallback**: If no DB preference, uses cookie
3. **Browser Detection**: If no cookie, uses Accept-Language
4. **Default**: Falls back to English

### Setting User Preferences
```typescript
// Set cookie preference (client-side)
document.cookie = 'locale=pt; path=/; max-age=31536000';
window.location.href = '/pt';

// Update database preference (server-side)
await updateUserProfile(userId, { language: 'pt' });
```

## 🛠️ Development Guidelines

### Best Practices
1. **Always use server components** for i18n when possible
2. **Use descriptive translation keys** (`user_profile_edit_button` not `btn1`)
3. **Group related translations** in objects
4. **Include SEO metadata** for all public pages
5. **Test all supported locales** before deployment

### Common Patterns
```tsx
// ✅ Good: Server component with translations
export default async function Page() {
  const t = await createTranslatorWithUserPreference('homepage');
  return <h1>{t('welcome_title')}</h1>;
}

// ❌ Avoid: Client-side locale detection
export default function Page() {
  const [locale, setLocale] = useState('en');
  useEffect(() => { /* client detection */ }, []);
}

// ✅ Good: Nested translation keys
t('user.profile.edit_button')

// ❌ Avoid: Flat keys
t('userProfileEditButton')
```

### Error Handling
The system automatically handles missing translations:
1. **Missing key**: Returns the key as fallback
2. **Missing file**: Falls back to English
3. **Missing English**: Returns empty object

## 🧪 Testing

### Manual Testing Checklist
- [ ] Root URL (`/`) redirects to user's preferred locale
- [ ] Direct locale URLs (`/pt`, `/en`, `/es`) work correctly
- [ ] Authenticated users get their database language preference
- [ ] Non-authenticated users get browser language detection
- [ ] SEO metadata appears in correct language
- [ ] Hreflang links are generated correctly

### Test Different Scenarios
```bash
# Test authenticated user with Portuguese preference
# Expected: / → /pt

# Test browser with Accept-Language: es-ES
# Expected: / → /es

# Test direct access to /en
# Expected: Page loads in English, no redirect
```

## 🚨 Troubleshooting

### Common Issues

#### 1. English text showing in other languages
```bash
# Check if translation key exists
grep -r "your_key" src/lib/i18n/messages/pt/

# Verify key usage
t('namespace.your_key') # ✅ Correct
t('your_key')          # ❌ Missing namespace
```

#### 2. Middleware redirect not working
```typescript
// Check middleware logs in development
console.log('[middleware] Processing request:', pathname);

// Verify cookie is set
document.cookie = 'locale=pt; path=/';
```

#### 3. Server component not getting locale
```typescript
// Debug locale detection
const locale = await getOptimalLocale();
console.log('Detected locale:', locale);

// Check headers
const headersList = await headers();
console.log('x-locale header:', headersList.get('x-locale'));
```

### Debug Mode
Enable detailed logging in development:
```typescript
// All i18n functions log debug info when NODE_ENV === 'development'
if (process.env.NODE_ENV === 'development') {
  console.log('[i18n] Debug info here');
}
```

## 🌟 Advanced Features

### Variable Interpolation
```json
{
  "welcome_message": "Welcome back, {{username}}!",
  "progress": "Step {{current}} of {{total}}"
}
```

```tsx
const message = t('welcome_message', { username: 'John' });
const progress = t('progress', { current: 2, total: 5 });
```

### Nested Keys
```json
{
  "user": {
    "profile": {
      "edit_button": "Edit Profile",
      "save_button": "Save Changes"
    }
  }
}
```

```tsx
const editButton = t('user.profile.edit_button');
```

### SEO Integration
```tsx
// Automatic hreflang generation
export async function generateMetadata() {
  const metadata = await generateSEOMetadata('homepage', 'main');
  
  return {
    title: metadata.title,
    description: metadata.description,
    alternates: {
      languages: metadata.hreflangLinks.reduce((acc, link) => {
        acc[link.hrefLang] = link.href;
        return acc;
      }, {})
    }
  };
}
```

## 📊 Performance Considerations

### Why Server-First?
- **Better SEO**: Search engines see fully translated content
- **No FOUC**: No flash of untranslated content
- **Faster Loading**: No client-side translation loading
- **Better Core Web Vitals**: No layout shifts from loading translations

### Caching
All translation functions use React `cache()` for optimal performance:
```typescript
export const getTranslations = cache(async (namespace: string) => {
  // Automatically cached per request
});
```

### Edge Runtime
Middleware runs on Edge Runtime for global performance:
- Faster redirects worldwide
- Lower latency for locale detection
- Better scalability

## 🔒 Security Considerations

### Input Validation
- All locale inputs are validated against supported locales
- Invalid locales fall back to default ('en')
- No user input directly affects translation loading

### XSS Prevention
- Translation keys are escaped by default
- Variable interpolation is safe
- No dynamic translation key generation from user input

## 📈 Migration from Legacy Systems

### If you have existing i18n code:
1. **Remove client-side locale detection** - Middleware handles this
2. **Replace useI18n hooks** with server-side functions
3. **Move translations** to the new namespace structure
4. **Update translation keys** to use snake_case
5. **Add SEO metadata** for all public pages

### Migration Checklist
- [ ] Remove client-side `useEffect` locale detection
- [ ] Replace `useState` locale management
- [ ] Convert to server components where possible
- [ ] Update translation file structure
- [ ] Add proper TypeScript types
- [ ] Test all locale scenarios

## 🎉 Success Metrics

After implementing this system, you should see:
- **Improved SEO**: Better search engine indexing for all locales
- **Better Performance**: Faster page loads with server-side rendering
- **Cleaner Code**: No duplicate locale detection logic
- **Better UX**: No flash of untranslated content
- **Easier Maintenance**: Single source of truth for all i18n logic

---

## 📚 Quick Reference

### Essential Functions
```typescript
// Get optimal locale for user
const locale = await getOptimalLocale();

// Create translator with user preferences
const t = await createTranslatorWithUserPreference('namespace');

// Generate SEO metadata
const metadata = await generateSEOMetadata('namespace', 'page');

// Get translations directly
const translations = await getTranslations('namespace', 'pt');
```

### Supported Locales
- `en` - English (default)
- `pt` - Portuguese (Brazil)
- `es` - Spanish (Latin America)

### File Locations
- **Middleware**: `middleware.ts` (handles all redirects)
- **Server Utils**: `src/lib/i18n/server.ts`
- **Types**: `src/lib/i18n/types/i18n.ts`
- **Translations**: `src/lib/i18n/messages/{locale}/{namespace}.json`

---

**This is the single source of truth for i18n in this application. All other i18n documentation is considered legacy and should not be referenced.**