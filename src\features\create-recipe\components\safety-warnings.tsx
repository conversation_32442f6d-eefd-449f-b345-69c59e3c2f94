/**
 * @fileoverview Safety warnings component for essential oil recipes
 * Shows age-appropriate safety warnings and guidelines
 */

'use client';

import React from 'react';
import { DemographicsData, SafetyWarning } from '../types/recipe.types';
import { getRecommendedDilution } from '../utils/safety-filter';
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from '@/components/ui/accordion';

interface SafetyWarningsProps {
  demographics: DemographicsData | null;
  customWarnings?: SafetyWarning[];
}

/**
 * Safety warnings component with age-appropriate guidelines
 */
export function SafetyWarnings({ demographics, customWarnings = [] }: SafetyWarningsProps) {
  // Always show the comprehensive safety protocol, even if demographics is null
  if (!demographics) {
    return (
      <div className="space-y-4">
        {/* Show comprehensive safety protocol even without demographics */}
        <ComprehensiveSafetyProtocol />
        
        {/* Show general safety guidelines for adults by default */}
        <GeneralSafetyGuidelines isChild={false} />
      </div>
    );
  }

  const isChild = demographics.specificAge < 10;
  const isTeenager = demographics.specificAge >= 10 && demographics.specificAge < 18;
  const recommendedDilution = getRecommendedDilution(demographics.specificAge);

  // Generate age-specific warnings
  const ageWarnings: SafetyWarning[] = [];

  if (isChild) {
    ageWarnings.push({
      warning_type: 'age_restriction',
      severity: 'high',
      message_localized: 'Atenção: Criança menor de 10 anos',
      guidance_localized: 'Use apenas óleos seguros para crianças com diluição máxima de 0,5%. Evite óleos dermocáusticos como canela, cravo e orégano.'
    });
  }

  if (isTeenager) {
    ageWarnings.push({
      warning_type: 'dilution',
      severity: 'medium',
      message_localized: 'Diluição recomendada para adolescentes',
      guidance_localized: `Use diluição máxima de ${recommendedDilution}% para segurança adequada.`
    });
  }

  // Combine all warnings
  const allWarnings = [...ageWarnings, ...customWarnings];

  if (allWarnings.length === 0) {
    return (
      <div className="bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-2xl p-6">
        <div className="flex items-center gap-3 mb-3">
          <CheckIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
          <h3 className="text-lg font-semibold text-green-800 dark:text-green-200">Perfil de Segurança</h3>
        </div>
        <p className="text-green-700 dark:text-green-300">
          Baseado na sua idade e perfil, as receitas recomendadas são seguras quando usadas conforme as instruções.
        </p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* High severity warnings */}
      {allWarnings.filter(w => w.severity === 'high').map((warning, index) => (
        <WarningCard key={`high-${index}`} warning={warning} />
      ))}

      {/* Medium severity warnings */}
      {allWarnings.filter(w => w.severity === 'medium').map((warning, index) => (
        <WarningCard key={`medium-${index}`} warning={warning} />
      ))}

      {/* Low severity warnings */}
      {allWarnings.filter(w => w.severity === 'low').map((warning, index) => (
        <WarningCard key={`low-${index}`} warning={warning} />
      ))}

      {/* Comprehensive Safety Protocol - from HTML accordion */}
      <ComprehensiveSafetyProtocol />

      {/* General safety guidelines */}
      <GeneralSafetyGuidelines isChild={isChild} />
    </div>
  );
}

/**
 * Individual warning card component
 */
interface WarningCardProps {
  warning: SafetyWarning;
}

function WarningCard({ warning }: WarningCardProps) {
  const severityConfig = {
    high: {
      bgColor: 'bg-destructive/10',
      borderColor: 'border-destructive/20',
      iconColor: 'text-destructive',
      textColor: 'text-destructive-foreground',
      icon: <AlertTriangleIcon />
    },
    medium: {
      bgColor: 'bg-yellow-50 dark:bg-yellow-950/20',
      borderColor: 'border-yellow-200 dark:border-yellow-800',
      iconColor: 'text-yellow-600 dark:text-yellow-400',
      textColor: 'text-yellow-800 dark:text-yellow-200',
      icon: <AlertCircleIcon />
    },
    low: {
      bgColor: 'bg-primary/10',
      borderColor: 'border-primary/20',
      iconColor: 'text-primary',
      textColor: 'text-primary-foreground',
      icon: <InfoIcon />
    }
  };

  const config = severityConfig[warning.severity];

  return (
    <div className={`${config.bgColor} ${config.borderColor} border rounded-2xl p-6`}>
      <div className="flex items-start gap-3">
        <span className={`${config.iconColor} flex-shrink-0 mt-0.5`}>
          {config.icon}
        </span>
        <div className="flex-1">
          <h4 className={`font-semibold ${config.textColor} mb-2`}>
            {warning.message_localized}
          </h4>
          <p className={`${config.textColor} text-sm leading-relaxed`}>
            {warning.guidance_localized}
          </p>
          {warning.warning_type && (
            <span className={`inline-block mt-2 px-2 py-1 rounded-full text-xs font-medium ${config.bgColor} ${config.textColor}`}>
              {getWarningTypeLabel(warning.warning_type)}
            </span>
          )}
        </div>
      </div>
    </div>
  );
}

/**
 * Comprehensive Safety Protocol component - from HTML accordion
 */
function ComprehensiveSafetyProtocol() {
  return (
    <div className="bg-card rounded-2xl p-6 shadow-lg border border-border">
      <div className="text-center mb-8">
        <h1 className="text-3xl font-bold text-card-foreground mb-2 flex items-center justify-center gap-3">
          <span className="text-3xl">🛡️</span>
          Protocolo de Segurança Essencial
        </h1>
        <p className="text-muted-foreground">Leia com atenção antes de utilizar qualquer receita.</p>
      </div>

      {/* Safety Accordion - matching HTML structure */}
      <Accordion type="multiple" className="space-y-2">
        {/* 1. Sensitivity Test (Mandatory) */}
        <AccordionItem value="sensitivity-test" className="bg-yellow-50 dark:bg-yellow-950/20 border-l-4 border-yellow-400 dark:border-yellow-600 rounded-r-lg border-0">
          <AccordionTrigger className="font-bold text-yellow-900 dark:text-yellow-100 px-4 py-4 hover:no-underline">
            1. Teste de Sensibilidade (Obrigatório)
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4">
            <p className="text-sm text-yellow-800 dark:text-yellow-200 mb-4">
              Antes do primeiro uso de qualquer nova mistura, realize este teste para prevenir reações alérgicas. É um passo crucial para a segurança.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <p className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">Como Fazer:</p>
                <ol className="list-decimal list-inside text-yellow-900 dark:text-yellow-200 space-y-2 pl-2">
                  <li>Prepare uma pequena quantidade da mistura.</li>
                  <li>Aplique uma gota na parte interna do antebraço.</li>
                  <li>Cubra a área com um curativo e aguarde 24 horas.</li>
                  <li>Após o período, remova o curativo e observe a pele.</li>
                </ol>
              </div>
              <div>
                <p className="font-semibold text-yellow-900 dark:text-yellow-100 mb-2">O que Observar:</p>
                <ul className="list-disc list-inside text-yellow-900 dark:text-yellow-200 space-y-2 pl-2">
                  <li>Vermelhidão ou irritação</li>
                  <li>Coceira intensa ou queimação</li>
                  <li>Inchaço, bolhas ou urticária</li>
                  <li>Qualquer forma de desconforto na área</li>
                </ul>
              </div>
            </div>
            <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-4">
              <strong>Resultado:</strong> Se notar qualquer uma dessas reações, não utilize a mistura. Lave a área com óleo vegetal (ex: coco, amêndoas) e depois com água e sabão.
            </p>
          </AccordionContent>
        </AccordionItem>

        {/* 2. Safe Use and Storage */}
        <AccordionItem value="safe-use-storage" className="bg-blue-50 dark:bg-blue-950/20 border-l-4 border-blue-400 dark:border-blue-600 rounded-r-lg border-0">
          <AccordionTrigger className="font-bold text-blue-900 dark:text-blue-100 px-4 py-4 hover:no-underline">
            2. Uso Seguro e Armazenamento
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm text-blue-900 dark:text-blue-200">
              <div>
                <h4 className="font-semibold mb-2">Diretrizes de Aplicação:</h4>
                <ul className="list-disc list-inside space-y-2 pl-2">
                  <li><strong>Sempre dilua</strong> os óleos essenciais em um óleo carreador, conforme a receita. Nunca aplique puros na pele.</li>
                  <li>Mantenha longe dos <strong>olhos, interior dos ouvidos e mucosas</strong>.</li>
                  <li>Evite aplicar as misturas próximo ao rosto de crianças.</li>
                  <li>O uso em crianças deve ser sempre <strong>supervisionado por um adulto</strong>.</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold mb-2">Armazenamento Correto:</h4>
                <ul className="list-disc list-inside space-y-2 pl-2">
                  <li>Guarde em frascos de vidro escuro (âmbar ou azul).</li>
                  <li>Mantenha em <strong>local fresco e ao abrigo da luz</strong> solar direta.</li>
                  <li>Certifique-se de que as tampas estejam bem fechadas.</li>
                  <li>Mantenha <strong>fora do alcance de crianças e animais</strong> de estimação.</li>
                </ul>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>

        {/* 3. Warning Signs and Necessary Action */}
        <AccordionItem value="warning-signs" className="bg-red-50 dark:bg-red-950/20 border-l-4 border-red-400 dark:border-red-600 rounded-r-lg border-0">
          <AccordionTrigger className="font-bold text-red-900 dark:text-red-100 px-4 py-4 hover:no-underline">
            3. Sinais de Alerta e Ação Necessária
          </AccordionTrigger>
          <AccordionContent className="px-4 pb-4">
            <p className="text-sm text-red-800 dark:text-red-200 mb-4">
              A aromaterapia é um suporte, mas não substitui a avaliação médica. Fique atento a estes sinais.
            </p>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 text-sm">
              <div>
                <h4 className="font-semibold text-red-900 dark:text-red-100 mb-2">Procure Ajuda Médica Imediata se:</h4>
                <ul className="list-disc list-inside text-red-900 dark:text-red-200 space-y-2 pl-2">
                  <li>Ocorrer uma <strong>reação alérgica grave</strong> (inchaço no rosto ou garganta, urticária generalizada).</li>
                  <li>Surgirem <strong>dificuldades respiratórias</strong> após o uso.</li>
                  <li>Os sintomas que você está tratando piorarem drasticamente.</li>
                </ul>
              </div>
              <div>
                <h4 className="font-semibold text-red-900 dark:text-red-100 mb-2">Consulte um Médico se a Condição Apresentar:</h4>
                <ul className="list-disc list-inside text-red-900 dark:text-red-200 space-y-2 pl-2">
                  <li>Dores de cabeça muito frequentes (&gt;3x/semana) ou incapacitantes.</li>
                  <li>Febre associada aos sintomas.</li>
                  <li>Vômitos recorrentes ou mudanças de comportamento.</li>
                  <li>Qualquer sintoma severo, persistente ou preocupante.</li>
                </ul>
              </div>
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      {/* Legal Notice and Responsibility - matching HTML structure */}
      <div className="bg-muted border-l-4 border-muted-foreground/30 rounded-r-lg p-6 shadow-sm mt-6">
        <h3 className="text-xl font-bold text-foreground mb-3 flex items-center gap-2">
          <span className="font-mono bg-muted-foreground/20 text-foreground rounded-full w-8 h-8 flex items-center justify-center text-lg">4</span>
          Aviso Legal e de Responsabilidade
        </h3>
        <div className="space-y-3 text-sm text-muted-foreground">
          <p>
            <strong>Natureza Complementar:</strong> A aromaterapia é uma prática de bem-estar e não substitui diagnósticos, tratamentos ou conselhos médicos. As informações e receitas aqui contidas são para fins educacionais.
          </p>
          <p>
            <strong>Responsabilidade Individual:</strong> O uso dos óleos essenciais é de sua inteira responsabilidade. As reações podem variar. Monitore sempre as respostas do corpo e ajuste o uso ou suspenda-o se necessário.
          </p>
          <p>
            <strong>Busca Profissional:</strong> Consulte sempre um profissional de saúde qualificado (médico ou aromaterapeuta certificado) antes de iniciar o uso de óleos essenciais, especialmente em crianças, gestantes, lactantes ou pessoas com condições médicas preexistentes.
          </p>
        </div>
      </div>
    </div>
  );
}

/**
 * General safety guidelines component
 */
interface GeneralSafetyGuidelinesProps {
  isChild: boolean;
}

function GeneralSafetyGuidelines({ isChild }: GeneralSafetyGuidelinesProps) {
  return (
    <div className="bg-muted/50 border border-border rounded-2xl p-6">
      <div className="flex items-center gap-3 mb-4">
        <BookIcon className="h-6 w-6 text-muted-foreground" />
        <h3 className="text-lg font-semibold text-foreground">
          Diretrizes Gerais de Segurança
        </h3>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <h4 className="font-semibold text-foreground mb-2">Antes do Uso:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Faça um teste de sensibilidade em pequena área da pele</li>
            <li>• Verifique se não há alergias conhecidas aos ingredientes</li>
            <li>• Consulte um profissional se estiver grávida ou amamentando</li>
            {isChild && <li>• Supervisão adulta é obrigatória para crianças</li>}
          </ul>
        </div>
        
        <div>
          <h4 className="font-semibold text-foreground mb-2">Durante o Uso:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Evite contato com olhos e mucosas</li>
            <li>• Não aplique em feridas abertas ou pele irritada</li>
            <li>• Interrompa o uso se houver reação adversa</li>
            <li>• Mantenha hidratação adequada</li>
          </ul>
        </div>
        
        <div>
          <h4 className="font-semibold text-foreground mb-2">Armazenamento:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Mantenha em local fresco e seco</li>
            <li>• Proteja da luz solar direta</li>
            <li>• Use frascos de vidro escuro</li>
            <li>• Mantenha fora do alcance de crianças</li>
          </ul>
        </div>
        
        <div>
          <h4 className="font-semibold text-foreground mb-2">Emergência:</h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Em caso de irritação, lave com água abundante</li>
            <li>• Se ingerido acidentalmente, procure ajuda médica</li>
            <li>• Mantenha os números de emergência à mão</li>
            <li>• Documente qualquer reação adversa</li>
          </ul>
        </div>
      </div>
    </div>
  );
}

/**
 * Get warning type label in Portuguese
 */
function getWarningTypeLabel(warningType: string): string {
  const labels: Record<string, string> = {
    age_restriction: 'Restrição de Idade',
    pregnancy: 'Gravidez/Amamentação',
    phototoxicity: 'Fotossensibilidade',
    dilution: 'Diluição',
    general: 'Geral'
  };
  
  return labels[warningType] || 'Aviso';
}

// ============================================================================
// ICON COMPONENTS
// ============================================================================

function AlertTriangleIcon() {
  return (
    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
    </svg>
  );
}

function AlertCircleIcon() {
  return (
    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );
}

function InfoIcon() {
  return (
    <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
    </svg>
  );
}

function CheckIcon({ className }: { className?: string }) {
  return (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
    </svg>
  );
}

function BookIcon({ className }: { className?: string }) {
  return (
    <svg className={className} fill="none" viewBox="0 0 24 24" stroke="currentColor">
      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
    </svg>
  );
}
