/**
 * Generate localized sitemaps for international SEO
 * Creates separate sitemaps for each language with proper hreflang
 */

const fs = require('fs');
const path = require('path');

const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'https://yoursite.com';
const locales = ['en', 'pt', 'es'];
const pages = [
  '/',
  '/create-recipe',
  '/dashboard',
  '/about',
  '/contact',
];

function generateSitemap() {
  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9" 
        xmlns:xhtml="http://www.w3.org/1999/xhtml">
${pages.map(page => 
  locales.map(locale => {
    const url = locale === 'en' ? `${baseUrl}${page}` : `${baseUrl}/${locale}${page}`;
    const alternates = locales.map(altLocale => {
      const altUrl = altLocale === 'en' ? `${baseUrl}${page}` : `${baseUrl}/${altLocale}${page}`;
      return `    <xhtml:link rel="alternate" hreflang="${altLocale}" href="${altUrl}" />`;
    }).join('\n');
    
    return `  <url>
    <loc>${url}</loc>
    <lastmod>${new Date().toISOString().split('T')[0]}</lastmod>
    <changefreq>weekly</changefreq>
    <priority>${page === '/' ? '1.0' : '0.8'}</priority>
${alternates}
  </url>`;
  }).join('\n')
).join('\n')}
</urlset>`;

  fs.writeFileSync(path.join(process.cwd(), 'public', 'sitemap.xml'), sitemap);
  console.log('✅ Sitemap generated successfully!');
}

function generateRobotsTxt() {
  const robots = `User-agent: *
Allow: /

# Sitemaps
Sitemap: ${baseUrl}/sitemap.xml

# Block admin areas
Disallow: /api/
Disallow: /_next/
Disallow: /admin/

# Allow all localized content
Allow: /en/
Allow: /pt/
Allow: /es/`;

  fs.writeFileSync(path.join(process.cwd(), 'public', 'robots.txt'), robots);
  console.log('✅ Robots.txt generated successfully!');
}

// Run the generators
generateSitemap();
generateRobotsTxt();

module.exports = { generateSitemap, generateRobotsTxt };