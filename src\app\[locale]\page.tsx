/**
 * Localized homepage with server-only i18n and user preference integration
 * Optimized for SEO while respecting user language preferences
 */

import { getServerLogger } from '@/lib/logger';
import { getServerAuthWithProfilePrefetch } from '@/lib/auth/server-auth.utils';
import { HydrationBoundary } from '@tanstack/react-query';
import {
  getOptimalLocale
} from '@/lib/i18n/server';
import { HomepageLayout } from '@/features/homepage/layout/homepage-layout';
import { Metadata } from 'next';

const logger = getServerLogger('LocalizedHomepage');

interface LocalizedHomepageProps {
  params: Promise<{ locale: string }>;
}

export async function generateMetadata({ params }: LocalizedHomepageProps): Promise<Metadata> {
  const { locale } = await params;
  const { getTranslations } = await import('next-intl/server');
  const t = await getTranslations('homepage');

  return {
    title: t('hero.title') + ' ' + t('hero.rotatingWords.0'),
    description: t('hero.subtitle'),
    alternates: {
      languages: {
        'en': '/en',
        'pt': '/pt',
        'es': '/es',
      },
    },
  };
}

/**
 * Optimized localized homepage component
 * Uses server-only i18n with user preference integration and efficient data prefetching
 */
export default async function LocalizedHomepage({ params }: LocalizedHomepageProps) {
  const { locale } = await params;

  // Debug logging
  if (process.env.NODE_ENV === 'development') {
    console.log('[LocalizedHomepage] Received params:', { locale });
    console.log('[LocalizedHomepage] About to call getOptimalLocale with:', locale);
  }

  try {
    // Get optimal locale (respects user preferences)
    const optimalLocale = await getOptimalLocale(locale);
    
    // Debug logging
    if (process.env.NODE_ENV === 'development') {
      console.log('[LocalizedHomepage] getOptimalLocale returned:', optimalLocale);
    }

    // Use centralized server auth utility (follows DRY principle)
    const { user, dehydratedState, error } = await getServerAuthWithProfilePrefetch({
      prefetchProfile: true, // Prefetch profile if user is authenticated
      profileTimeout: 500,
      profileStaleTime: 10 * 1000,
      requestId: `homepage-${locale}-${Date.now()}`,
      debugMode: process.env.NODE_ENV === 'development'
    });



    if (error) {
      // Error already logged in centralized utility, just add context
      logger.warn('Auth error in localized homepage', {
        error: error.message,
        locale: optimalLocale,
        operation: 'LocalizedHomepage'
      });
    }

    if (user?.id) {
      // Mask userId for privacy in logs
      const maskedUserId = `${user.id.substring(0, 6)}...`;

      logger.info('Localized homepage loaded for authenticated user', {
        userId: maskedUserId,
        locale: optimalLocale,
        urlLocale: locale,
        operation: 'LocalizedHomepage'
      });
    } else {
      logger.info('Localized homepage loaded for anonymous user', {
        locale: optimalLocale,
        urlLocale: locale,
        operation: 'LocalizedHomepage'
      });
    }

    return (
      <HydrationBoundary state={dehydratedState}>
        <HomepageLayout />
      </HydrationBoundary>
    );
  } catch (err) {
    logger.error('Critical error in localized homepage', {
      error: err instanceof Error ? err.message : String(err),
      stack: err instanceof Error ? err.stack : undefined,
      locale,
      operation: 'LocalizedHomepage'
    });

    // Fallback to basic layout without hydrated state
    return (
      <HomepageLayout />
    );
  }
}

export async function generateStaticParams() {
  return [
    { locale: 'en' },
    { locale: 'pt' },
    { locale: 'es' },
  ];
}
