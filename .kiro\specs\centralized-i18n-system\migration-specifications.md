# Centralized i18n System - Migration Specifications

## Overview

This document provides comprehensive specifications for migrating existing user-facing feature components to use the centralized i18n system. The migration focuses on **in-place editing** of existing files rather than creating duplicates, ensuring a clean and maintainable codebase.

## Migration Strategy

### Core Principles
1. **In-Place Migration**: Edit existing files directly, no duplication
2. **Server-First Approach**: Prioritize server-side translations for optimal SEO and performance
3. **Minimal Disruption**: Maintain existing functionality while adding i18n support
4. **Incremental Migration**: Components can be migrated one at a time
5. **Type Safety**: Maintain full TypeScript support throughout migration

### Migration Patterns

#### Pattern 1: Client Component to Server Component (Preferred)
```typescript
// BEFORE: Client component with client-side i18n
'use client';
import { useTranslations } from '@/lib/i18n';

export default function MyComponent() {
  const t = useTranslations();
  return <h1>{t('title')}</h1>;
}

// AFTER: Server component with server-side i18n
import { createTranslatorWithUserPreference } from '@/lib/i18n/server';

export default async function MyComponent() {
  const t = await createTranslatorWithUserPreference('my-namespace');
  return <h1>{t('title')}</h1>;
}
```

#### Pattern 2: Client Component with Server-Provided Translations
```typescript
// BEFORE: Client component with client-side i18n
'use client';
import { useTranslations } from '@/lib/i18n';

export default function MyComponent() {
  const t = useTranslations();
  return <h1>{t('title')}</h1>;
}

// AFTER: Client component with server-provided translations
'use client';
import { useServerI18n } from '@/hooks/use-server-i18n';

interface MyComponentProps {
  translations: Record<string, any>;
  locale: SupportedLocale;
}

export default function MyComponent({ translations, locale }: MyComponentProps) {
  const { t } = useServerI18n({ translations, locale });
  return <h1>{t('title')}</h1>;
}
```

#### Pattern 3: Page Component Migration
```typescript
// BEFORE: Page with getServerSideProps
export const getServerSideProps: GetServerSideProps = async (context) => {
  return getI18nServerSideProps(context);
};

// AFTER: App Router page with server-side i18n
import { generateSEOMetadata } from '@/lib/i18n/server';

export async function generateMetadata() {
  return await generateSEOMetadata('my-namespace', 'page');
}

export default async function MyPage() {
  const t = await createTranslatorWithUserPreference('my-namespace');
  return <h1>{t('page_title')}</h1>;
}
```

## Step-by-Step Migration Process

### Phase 1: Pre-Migration Analysis

#### 1.1 Identify Component Type
- **Server Component**: Can be migrated directly to server-side i18n
- **Client Component**: Requires server-provided translations pattern
- **Page Component**: Migrate to App Router with server-side i18n

#### 1.2 Analyze Translation Usage
- Identify all translation keys used in the component
- Check if translations exist in the new namespace structure
- Note any dynamic translation patterns or variable interpolation

#### 1.3 Check Dependencies
- Identify child components that also need migration
- Check for any client-side locale detection patterns
- Verify no circular dependencies exist

### Phase 2: Translation File Preparation

#### 2.1 Create Namespace Translation Files
```bash
# Create translation files for the component's namespace
touch src/lib/i18n/messages/en/{namespace}.json
touch src/lib/i18n/messages/pt/{namespace}.json
touch src/lib/i18n/messages/es/{namespace}.json
```

#### 2.2 Migrate Translation Keys
```json
// src/lib/i18n/messages/en/create-recipe.json
{
  "title": "Create Recipe",
  "subtitle": "Build your personalized health recipe",
  "steps": {
    "health_concern": {
      "title": "Health Concern",
      "description": "What health issue would you like to address?"
    }
  },
  "navigation": {
    "next": "Next Step",
    "previous": "Previous Step",
    "progress": "Step {{current}} of {{total}}"
  },
  "meta_title": "Create Recipe - Health App",
  "meta_description": "Create personalized health recipes"
}
```

#### 2.3 Update Translation Key Format
- Convert from `camelCase` to `snake_case`
- Use namespace prefixes: `create-recipe:title` → `title`
- Group related keys in objects

### Phase 3: Component Migration

#### 3.1 Server Component Migration

**Step 1**: Remove client directive and client-side imports
```typescript
// Remove these lines
'use client';
import { useTranslations } from '@/lib/i18n';
```

**Step 2**: Add server-side imports
```typescript
import { createTranslatorWithUserPreference } from '@/lib/i18n/server';
import type { SupportedLocale } from '@/lib/i18n/types/i18n';
```

**Step 3**: Convert function to async and add translation logic
```typescript
export default async function MyComponent() {
  const t = await createTranslatorWithUserPreference('my-namespace');
  
  // Rest of component logic remains the same
  return <h1>{t('title')}</h1>;
}
```

#### 3.2 Client Component Migration

**Step 1**: Update component props interface
```typescript
interface MyComponentProps {
  locale: SupportedLocale;
  translations: Record<string, Record<string, any>>;
  // ... existing props
}
```

**Step 2**: Replace useTranslations with useServerI18n
```typescript
// Replace this
const t = useTranslations();

// With this
const { t } = useServerI18n({ locale, translations });
```

**Step 3**: Update parent component to provide translations
```typescript
// In parent server component
export default async function ParentComponent() {
  const locale = await getOptimalLocale();
  const translations = {
    'my-namespace': await getTranslations('my-namespace', locale)
  };
  
  return (
    <MyClientComponent 
      locale={locale}
      translations={translations}
      // ... other props
    />
  );
}
```

#### 3.3 Page Component Migration

**Step 1**: Remove Pages Router patterns
```typescript
// Remove these
import { GetServerSideProps } from 'next';
import { getI18nServerSideProps } from '@/lib/i18n/server';

export const getServerSideProps: GetServerSideProps = async (context) => {
  return getI18nServerSideProps(context);
};
```

**Step 2**: Add App Router metadata generation
```typescript
import { generateSEOMetadata } from '@/lib/i18n/server';

export async function generateMetadata() {
  return await generateSEOMetadata('my-namespace', 'page');
}
```

**Step 3**: Convert to server component
```typescript
export default async function MyPage() {
  const t = await createTranslatorWithUserPreference('my-namespace');
  
  return (
    <div>
      <h1>{t('page_title')}</h1>
      <p>{t('page_description')}</p>
    </div>
  );
}
```

### Phase 4: Testing and Validation

#### 4.1 Functional Testing
- [ ] Component renders correctly in all supported locales
- [ ] Translation keys resolve to correct values
- [ ] Variable interpolation works correctly
- [ ] Fallback behavior works for missing translations

#### 4.2 Performance Testing
- [ ] Server-side rendering works correctly
- [ ] No client-side hydration issues
- [ ] Translation loading is cached properly
- [ ] No unnecessary re-renders

#### 4.3 SEO Testing
- [ ] Metadata is generated correctly
- [ ] Hreflang links are present
- [ ] Content is fully translated on initial load
- [ ] No flash of untranslated content (FOUC)

## Migration Guidelines by Component Type

### Create Recipe Feature Components

#### High Priority (User-Facing)
1. **Mobile Layout Components**
   - `src/features/create-recipe/components/mobile-layout.tsx`
   - Pattern: Client component with server-provided translations
   - Namespace: `create-recipe`

2. **Auth Guard Components**
   - `src/features/create-recipe/components/auth-guard.tsx`
   - Pattern: Client component with server-provided translations
   - Namespace: `auth`

3. **Error Boundary Components**
   - `src/features/create-recipe/components/error-boundary.tsx`
   - Pattern: Client component with server-provided translations
   - Namespace: `common`

4. **Loading Skeleton Components**
   - `src/features/create-recipe/components/loading-skeletons.tsx`
   - Pattern: Client component with server-provided translations
   - Namespace: `common`

#### Medium Priority (Supporting)
1. **Page Components**
   - `pages/create-recipe.tsx`
   - Pattern: Convert to App Router page
   - Namespace: `create-recipe`

2. **App Component**
   - `pages/_app.tsx`
   - Pattern: Convert to App Router layout
   - Namespace: `common`

### Homepage Feature Components

#### Already Migrated (Reference Implementation)
- `src/features/homepage/layout/homepage-layout-i18n.tsx` - **Reference pattern**
- Shows how to implement server-provided translations for client components

### Common Patterns

#### Hook Migration
```typescript
// BEFORE: Client-side hook
import { useTranslations } from '@/lib/i18n';

function MyComponent() {
  const t = useTranslations();
  return <span>{t('common:loading')}</span>;
}

// AFTER: Server-provided translations
interface MyComponentProps {
  translations: Record<string, Record<string, any>>;
  locale: SupportedLocale;
}

function MyComponent({ translations, locale }: MyComponentProps) {
  const { t } = useServerI18n({ translations, locale });
  return <span>{t('common:loading')}</span>;
}
```

#### SEO Component Migration
```typescript
// BEFORE: LocalizedHead component
import { LocalizedHead } from '@/components/seo/LocalizedHead';

// AFTER: generateMetadata function
export async function generateMetadata() {
  return await generateSEOMetadata('namespace', 'page');
}
```

## Code Transformation Patterns

### Import Statement Updates

#### Server Components
```typescript
// Remove
import { useTranslations } from '@/lib/i18n';

// Add
import { createTranslatorWithUserPreference, getOptimalLocale } from '@/lib/i18n/server';
import type { SupportedLocale } from '@/lib/i18n/types/i18n';
```

#### Client Components
```typescript
// Remove
import { useTranslations } from '@/lib/i18n';

// Add
import { useServerI18n } from '@/hooks/use-server-i18n';
import type { SupportedLocale } from '@/lib/i18n/types/i18n';
```

### Function Signature Updates

#### Server Components
```typescript
// Before
export default function MyComponent() {

// After
export default async function MyComponent() {
```

#### Client Components
```typescript
// Before
export default function MyComponent() {

// After
interface MyComponentProps {
  locale: SupportedLocale;
  translations: Record<string, Record<string, any>>;
}

export default function MyComponent({ locale, translations }: MyComponentProps) {
```

### Translation Usage Updates

#### Basic Translation
```typescript
// Before
const t = useTranslations();
const title = t('create-recipe:title');

// After (Server)
const t = await createTranslatorWithUserPreference('create-recipe');
const title = t('title');

// After (Client)
const { t } = useServerI18n({ locale, translations });
const title = t('create-recipe:title');
```

#### Variable Interpolation
```typescript
// Before
const message = t('create-recipe:progress', undefined, { current: 2, total: 5 });

// After (Server)
const message = t('progress', { current: 2, total: 5 });

// After (Client)
const message = t('create-recipe:progress', { current: 2, total: 5 });
```

## Common Migration Challenges and Solutions

### Challenge 1: Client Component Needs Server Data
**Problem**: Client component needs translations but can't use server-side functions
**Solution**: Use server-provided translations pattern

```typescript
// Parent server component provides translations
export default async function ParentServer() {
  const locale = await getOptimalLocale();
  const translations = {
    'namespace': await getTranslations('namespace', locale)
  };
  
  return <ClientChild locale={locale} translations={translations} />;
}
```

### Challenge 2: Dynamic Translation Keys
**Problem**: Translation keys are generated dynamically
**Solution**: Pre-load all possible translations or use computed keys

```typescript
// Before
const dynamicKey = `step_${currentStep}_title`;
const title = t(dynamicKey);

// After - Pre-load all step translations
const stepTranslations = {
  step_1_title: t('step_1_title'),
  step_2_title: t('step_2_title'),
  // ...
};
const title = stepTranslations[`step_${currentStep}_title`];
```

### Challenge 3: Nested Component Translation Passing
**Problem**: Deep component tree needs translations
**Solution**: Use React Context or prop drilling

```typescript
// Context approach
const I18nContext = createContext<{
  t: (key: string) => string;
  locale: SupportedLocale;
}>();

// Provider in parent
<I18nContext.Provider value={{ t, locale }}>
  <DeepChildComponent />
</I18nContext.Provider>

// Consumer in child
const { t } = useContext(I18nContext);
```

## Validation Procedures

### Pre-Migration Checklist
- [ ] Component analysis completed
- [ ] Translation files created for all locales
- [ ] Dependencies identified and planned
- [ ] Migration pattern selected

### During Migration Checklist
- [ ] Imports updated correctly
- [ ] Function signatures updated
- [ ] Translation usage updated
- [ ] Props interfaces updated (for client components)
- [ ] Parent components updated (for client components)

### Post-Migration Checklist
- [ ] Component renders without errors
- [ ] All translations display correctly
- [ ] Variable interpolation works
- [ ] Fallback behavior tested
- [ ] Performance impact assessed
- [ ] SEO metadata generated correctly

### Testing Procedures

#### Unit Testing
```typescript
// Test translation resolution
describe('MyComponent i18n', () => {
  it('should display translated title', async () => {
    const component = await MyComponent();
    expect(component).toContain('Expected Title');
  });
  
  it('should handle missing translations gracefully', async () => {
    // Test fallback behavior
  });
});
```

#### Integration Testing
```typescript
// Test full page rendering
describe('Page i18n integration', () => {
  it('should render page in Portuguese', async () => {
    // Mock locale detection
    // Render page
    // Verify Portuguese content
  });
});
```

#### Manual Testing
1. **Locale Switching**: Test all supported locales
2. **Fallback Behavior**: Test with missing translation keys
3. **Variable Interpolation**: Test dynamic content
4. **SEO**: Verify metadata and hreflang links
5. **Performance**: Check for hydration issues

## Best Practices

### Do's
- ✅ Use server components when possible for better SEO
- ✅ Group related translation keys in objects
- ✅ Use descriptive, snake_case translation keys
- ✅ Include SEO metadata for all public pages
- ✅ Test all supported locales
- ✅ Handle missing translations gracefully

### Don'ts
- ❌ Don't create duplicate components for testing
- ❌ Don't use client-side locale detection
- ❌ Don't hardcode translation strings
- ❌ Don't skip fallback handling
- ❌ Don't ignore TypeScript errors
- ❌ Don't forget to update parent components

### Performance Considerations
- Use React `cache()` for translation functions
- Minimize client-side JavaScript for i18n
- Pre-load translations at build time when possible
- Use server-side rendering for better Core Web Vitals

### Accessibility Considerations
- Include `lang` attribute on HTML elements
- Provide translated alt text for images
- Ensure proper reading order in all languages
- Test with screen readers in different locales

## Conclusion

This migration specification provides a comprehensive, repeatable process for migrating existing components to the centralized i18n system. By following these patterns and procedures, developers can ensure consistent, performant, and maintainable internationalization across the entire application.

The key to successful migration is the **in-place editing approach** that maintains code quality while adding i18n support incrementally, without disrupting the existing codebase structure.