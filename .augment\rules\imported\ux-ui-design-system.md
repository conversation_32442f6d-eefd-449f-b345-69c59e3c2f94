---
type: "manual"
---

# UX/UI Design System Rules

## Tables
- Use semantic components: `<Table>`, `<TableHeader>`, `<TableBody>`, `<TableRow>`, `<TableCell>`, `<TableHead>`.
- Wrap tables in a container with these classes:  
  `rounded-lg border border-border/50 bg-card/50 backdrop-blur-sm overflow-hidden shadow-sm`
- Table headers:  
  `bg-muted/30`, bold text, `text-foreground`
- Each table row:  
  `border-b border-border/30 last:border-b-0`
- On hover, table rows:  
  `hover:bg-accent/50 hover:shadow-sm`
- Selected rows:  
  `bg-primary/5 border-primary/20`
- All interactive rows:  
  `cursor-pointer`

## Selection Patterns (Tables)
- The first column should have a custom checkbox or indicator:
  - Unselected: bordered, background matches table, subtle hover border.
  - Selected: filled with primary color, checkmark icon, border matches primary.
- Clicking a row toggles its selection.

## Chip Tags
- Use for displaying selected itens and tag, such as causes, symptoms, or categories.
- Render as:  
  `inline-flex items-center rounded-full border px-3 py-1.5 text-xs font-medium`
- Add a colored dot at the start for category (e.g., `bg-chart-2` for causes, `bg-chart-3` for symptoms).
- Use `bg-muted` for background, `border-border` for border, and text color to match the category.
- On hover:  
  `hover:bg-chart-2/10 hover:border-chart-2/30` (for causes)  
  `hover:bg-chart-3/10 hover:border-chart-3/30` (for symptoms)

## Badges
- **RelevancyBadge:** Place in its own cell, visually distinct but not overwhelming.
- **Count Badge:** Use `<Badge variant="outline">` for counts.
- **SafetyStatusBadge:** Compact, color-coded, group multiple statuses together.

## Hover & Focus States
- All interactive elements (rows, chip tags, badges) must have a visible hover state.
- Use `transition-colors duration-200` for smooth color transitions.
- Provide clear focus states for keyboard users.

## Accessibility & Feedback
- Use ARIA roles and labels for tables and interactive elements.
- Error states: use red color and clear messaging.
- Loading states: use an animated spinner.

---

**If in doubt, check `causes-selection.tsx`, `symptoms-selection.tsx`, or `therapeutic-properties-table.tsx` for examples.**

# Input Sections

- Always provide a clear, prominent label or title above the input (e.g., "Create Your Recipe").
- Add a short description and a tip/subtitle to guide the user.
- Show example input cards above or near the input. Each example should be a button with:
  - Classes: `p-4 text-left bg-card border border-border rounded-lg`
  - On hover: `hover:bg-accent hover:text-accent-foreground transition-colors`
- Use a `<Textarea>` for multi-line input:
  - Classes: `w-full px-4 py-3 resize-none bg-transparent border-none text-foreground text-sm`
  - Placeholder: clear, helpful, and in muted color (`placeholder:text-muted-foreground placeholder:text-sm`)
  - Rounded corners: `rounded-xl`
  - Container: `bg-card rounded-xl border border-border shadow-md`
  - Auto-resize the textarea as the user types (see useAutoResizeTextarea pattern)
  - Character limit (e.g., 500) and show a live character counter
  - If near the limit, counter turns orange; at the limit, turns red
- Show a toolbar below the input with:
  - Character counter (`text-xs`, color changes as above)
  - Saving indicator (spinner + "Saving..." text) when autosaving
  - "Continue" button:
    - Only enabled if input is valid (e.g., 3–500 chars)
    - Classes: `bg-primary text-primary-foreground hover:bg-primary/90` when enabled, `bg-muted text-muted-foreground cursor-not-allowed` when disabled
    - Icon: ArrowUp
    - Accessible label and screen-reader text
- Support keyboard submit: Enter submits, Shift+Enter adds a new line
- Always provide clear focus and hover states for all interactive elements
- Input must be accessible: use ARIA labels, roles, and ensure keyboard navigation

---

Example classes and structure are based on `health-concern-chat-input.tsx`.

---

**If in doubt, check `causes-selection.tsx`, `symptoms-selection.tsx`, or `therapeutic-properties-table.tsx` for examples.**