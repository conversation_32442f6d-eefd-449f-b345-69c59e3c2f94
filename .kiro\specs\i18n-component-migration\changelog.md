feat(i18n): integrate next-intl for improved localization support

- Added next-intl package to package.json for internationalization.
- Updated layout component to use NextIntlClientProvider for message handling.
- Refactored homepage to utilize next-intl for translations, removing custom i18n context.
- Simplified hero content component to directly use next-intl's useTranslations hook.
- Removed unnecessary wrapper components for i18n, streamlining the codebase.
- Enhanced request handling for locale validation and message loading.
- Established guidelines for i18n library usage and best practices.