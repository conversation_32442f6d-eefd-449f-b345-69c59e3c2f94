import { redirect } from 'next/navigation';

/**
 * Root page fallback component
 * This page should rarely be reached since middleware handles all redirects.
 * If this page is accessed, it means middleware redirect failed or was bypassed.
 */
export default async function RootPage() {
  // Log that this fallback page was accessed for monitoring
  console.warn('[i18n] Root page fallback accessed - middleware redirect may have failed');
  
  if (process.env.NODE_ENV === 'development') {
    console.log('[i18n] Root page component reached - this should be rare since middleware handles redirects');
  }

  // Fallback redirect to English if middleware somehow failed
  redirect('/en');
}

