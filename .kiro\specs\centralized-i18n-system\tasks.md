# Implementation Plan

- [x] 1. Enhance middleware to handle root URL redirects with authentication



  - Move locale detection logic from `/app/page.tsx` to middleware
  - Integrate existing `getServerAuthState` and `getCurrentUserProfile` services in middleware
  - Remove duplicate `parseAcceptLanguage` function from `/app/page.tsx` (use middleware version)
  - Add authentication-based locale preference detection to middleware
  - Ensure middleware handles both authenticated and non-authenticated users
  - _Requirements: 2.1, 2.2, 2.3, 2.4_

- [x] 2. Update middleware to use existing i18n services



  - Import and utilize existing `getUserPreferredLocale` from `src/lib/i18n/user-preference-redirect.ts`
  - Integrate with existing `SupportedLocale` type from `src/lib/i18n/types/i18n.ts`
  - Ensure middleware uses existing locale detection priority system
  - Maintain compatibility with existing `x-locale` and `x-detected-locale` headers
  - _Requirements: 2.1, 2.2, 4.4, 4.5_

- [x] 3. Simplify root page component






  - Remove all redirect logic from `/app/page.tsx`
  - Remove duplicate `parseAcceptLanguage` function
  - Remove auth service calls (`getServerAuthState`, `getCurrentUserProfile`)
  - Create simple fallback page that should rarely be reached
  - Add logging to track if fallback page is ever accessed
  - _Requirements: 2.1, 2.2, 5.1, 5.4_

- [x] 4. Test middleware integration with existing i18n system


  - Verify authenticated users are redirected to their database language preference
  - Test non-authenticated users get browser language detection
  - Confirm existing localized pages (`/[locale]/page.tsx`) continue working
  - Validate existing server-only translation system remains functional
  - Test existing SEO metadata and hreflang links still work
  - _Requirements: 4.1, 4.2, 4.3, 4.4, 4.5_

- [x] 5. Validate DRY compliance and clean up


  - Confirm no duplicate locale detection logic exists
  - Verify single source of truth for redirect logic (middleware only)
  - Test that auth services are properly cached and not called redundantly
  - Remove any unused imports or functions from cleaned up files
  - Update any comments or documentation that reference old redirect logic
  - _Requirements: 2.1, 2.2, 5.2, 5.3_

- [x] 6. Relocate i18n request configuration to proper architecture location


  - Move `src/i18n/request.ts` to `src/lib/i18n/request.ts` to follow established patterns (done that already)
  - Update any imports that reference the old location (needs to be done)
  - Ensure Next.js configuration still finds the relocated file (needs to be done)
  - Test that server-side translations continue working after relocation (needs to be done)
  - _Requirements: 1.1, 1.2, 1.3_

- [x] 7. Create legacy cleanup documentation



  - Document all files and functions that become legacy after implementation
  - Create list of client-side detection patterns that can be removed in future
  - Prepare migration path for any remaining client-side i18n logic
  - Document the centralized architecture for future developers
  - _Requirements: 4.5, 5.4_